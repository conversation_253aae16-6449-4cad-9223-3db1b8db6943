package repository

import (
	"context"
	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
	"pxpat-backend/internal/content-cluster/video-service/client"
	"pxpat-backend/internal/content-cluster/video-service/dto"
	"pxpat-backend/internal/content-cluster/video-service/model"
	"pxpat-backend/pkg/cache"
)

type ContentRepository struct {
	db                *gorm.DB
	rdb               *redis.Client
	cacheManage       cache.Manager
	userServiceClient client.UserServiceClient
}

func NewContentRepository(db *gorm.DB, rdb *redis.Client, cacheManage cache.Manager, userServiceClient client.UserServiceClient) *ContentRepository {
	return &ContentRepository{
		db:                db,
		rdb:               rdb,
		cacheManage:       cacheManage,
		userServiceClient: userServiceClient,
	}
}

// GetDB 获取数据库实例，用于事务处理
func (r *ContentRepository) GetDB() *gorm.DB {
	return r.db
}

func (r *ContentRepository) PublishVideo(ctx context.Context, data *model.Content) error {
	log.Debug().
		Str("content_ksuid", data.ContentKSUID).
		Str("title", data.Title).
		Str("video_id", data.VideoID).
		Msg("开始创建内容记录到数据库")

	err := r.db.WithContext(ctx).Create(data).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("content_ksuid", data.ContentKSUID).
			Str("title", data.Title).
			Str("video_id", data.VideoID).
			Msg("创建内容记录到数据库失败")
		return err
	}

	log.Debug().
		Str("content_ksuid", data.ContentKSUID).
		Str("title", data.Title).
		Str("video_id", data.VideoID).
		Msg("创建内容记录到数据库成功")

	return nil
}

func (r *ContentRepository) GetContentByContentKSUID(ctx context.Context, contentKSUID string) (*model.Content, error) {
	log.Debug().
		Str("content_ksuid", contentKSUID).
		Msg("开始从数据库获取内容详情")

	var content model.Content
	err := r.db.WithContext(ctx).
		Preload("Category").
		Preload("Tags").
		Preload("Collaborators", func(db *gorm.DB) *gorm.DB {
			return db.Select("id,content_ksuid,user_ksuid,role_name,role_order").Where("is_uploader = ?", false)
		}).
		Where("content_ksuid = ?", contentKSUID).
		First(&content).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("content_ksuid", contentKSUID).
			Msg("从数据库获取内容详情失败")
		return nil, err
	}

	log.Debug().
		Str("content_ksuid", contentKSUID).
		Str("title", content.Title).
		Str("status", string(content.Status)).
		Msg("从数据库获取内容详情成功")

	return &content, nil
}

func (r *ContentRepository) VideoIDExist(ctx context.Context, videoID string) (bool, error) {
	log.Debug().
		Str("video_id", videoID).
		Msg("开始检查VideoID是否存在")

	var count int64
	err := r.db.WithContext(ctx).Model(&model.Content{}).Where("video_id = ?", videoID).Count(&count).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("video_id", videoID).
			Msg("检查VideoID是否存在时数据库查询失败")
		return false, err
	}

	exists := count > 0
	log.Debug().
		Str("video_id", videoID).
		Int64("count", count).
		Bool("exists", exists).
		Msg("检查VideoID是否存在完成")

	return exists, nil
}

// UpdateContentStatus 更新内容状态
func (r *ContentRepository) UpdateContentStatus(ctx context.Context, contentKSUID string, status model.ContentStatus) error {
	log.Debug().
		Str("content_ksuid", contentKSUID).
		Str("status", string(status)).
		Msg("开始更新内容状态到数据库")

	result := r.db.WithContext(ctx).
		Model(&model.Content{}).
		Where("content_ksuid = ?", contentKSUID).
		Update("status", status)

	if result.Error != nil {
		log.Error().
			Err(result.Error).
			Str("content_ksuid", contentKSUID).
			Str("status", string(status)).
			Msg("更新内容状态到数据库失败")
		return result.Error
	}

	log.Info().
		Str("content_ksuid", contentKSUID).
		Str("status", string(status)).
		Int64("rows_affected", result.RowsAffected).
		Msg("更新内容状态到数据库成功")

	return nil
}

// UpdateMediaResultInfo 更新媒体信息
func (r *ContentRepository) UpdateMediaResultInfo(ctx context.Context, event *dto.TranscodingOKEvent) error {
	log.Info().
		Str("content_ksuid", event.ContentKSUID).
		Uint64("media_result_id", event.MediaResultID).
		Msg("开始更新媒体信息到数据库")

	updates := map[string]interface{}{
		"media_result_id": event.MediaResultID,
		"duration":        event.Duration,
		"orientation":     event.Orientation,
	}

	// 新增：更新媒体处理结果数据
	if event.Plays != "" {
		updates["plays"] = event.Plays
	}
	if event.KeyFrames != "" {
		updates["key_frames"] = event.KeyFrames
	}
	if event.PreviewVideo != "" {
		updates["preview_video"] = event.PreviewVideo
	}
	if event.IsAuditTransCode {
		updates["is_transcode_audit_video"] = true
	}

	result := r.db.WithContext(ctx).
		Model(&model.Content{}).
		Where("content_ksuid = ?", event.ContentKSUID).
		Updates(updates)

	if result.Error != nil {
		log.Error().
			Err(result.Error).
			Str("content_ksuid", event.ContentKSUID).
			Uint64("media_result_id", event.MediaResultID).
			Msg("更新媒体信息到数据库失败")
		return result.Error
	}

	log.Debug().
		Str("content_ksuid", event.ContentKSUID).
		Uint64("media_result_id", event.MediaResultID).
		Int64("rows_affected", result.RowsAffected).
		Msg("更新媒体信息到数据库成功")

	return nil
}

// GetPublishedContentsByType 根据类型获取已发布的内容，支持分页和排序
func (r *ContentRepository) GetPublishedContentsByType(ctx context.Context, contentType string, page, pageSize int, sortBy, sourceType, userKSUID string, tagIDs []uint, categoryID uint, level string) ([]*model.Content, int64, error) {
	log.Debug().
		Str("content_type", contentType).
		Int("page", page).
		Int("page_size", pageSize).
		Str("sort_by", sortBy).
		Str("source_type", sourceType).
		Str("user_ksuid", userKSUID).
		Interface("tag_ids", tagIDs).
		Uint("category_id", categoryID).
		Str("level", level).
		Msg("开始从数据库获取指定类型的已发布内容列表")

	var contents []*model.Content
	var total int64

	// 构建基础查询
	query := r.db.WithContext(ctx).Model(&model.Content{}).
		Where("status = ? AND type = ?", model.StatusPublished, contentType)

	// 添加来源类型筛选
	if sourceType != "" {
		query = query.Where("source_type = ?", sourceType)
	}

	// 添加投稿人筛选
	if userKSUID != "" {
		query = query.Where("user_ksuid = ?", userKSUID)
	}

	// 添加分类筛选
	if categoryID > 0 {
		query = query.Where("category_id = ?", categoryID)
	}

	// 添加审核级别筛选
	if level != "" {
		query = query.Where("level = ?", level)
	}

	// 添加标签筛选
	if len(tagIDs) > 0 {
		// 使用子查询来筛选包含指定标签的内容
		query = query.Where("content_ksuid IN (?)",
			r.db.Table("video_content_tags").
				Select("content_ksuid").
				Where("tag_id IN ?", tagIDs).
				Group("content_ksuid").
				Having("COUNT(DISTINCT tag_id) = ?", len(tagIDs)))
	}

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("content_type", contentType).
			Int("page", page).
			Int("page_size", pageSize).
			Str("sort_by", sortBy).
			Str("source_type", sourceType).
			Str("user_ksuid", userKSUID).
			Interface("tag_ids", tagIDs).
			Uint("category_id", categoryID).
			Str("level", level).
			Msg("获取指定类型已发布内容总数失败")
		return nil, 0, err
	}

	// 计算偏移量
	offset := (page - 1) * pageSize

	// 根据排序方式构建ORDER BY子句
	orderClause := r.buildOrderClause(sortBy)

	// 获取分页数据
	err = query.
		Select("user_ksuid, content_ksuid, title, cover_url, created_at, view_count, like_count, comment_count, favorite_count, video_id, duration, orientation, preview_video").
		Order(orderClause).
		Offset(offset).
		Limit(pageSize).
		Find(&contents).Error

	if err != nil {
		log.Error().
			Err(err).
			Str("content_type", contentType).
			Int("page", page).
			Int("page_size", pageSize).
			Str("sort_by", sortBy).
			Str("source_type", sourceType).
			Str("user_ksuid", userKSUID).
			Interface("tag_ids", tagIDs).
			Uint("category_id", categoryID).
			Str("level", level).
			Int64("total", total).
			Msg("获取指定类型已发布内容列表失败")
		return nil, 0, err
	}

	log.Debug().
		Str("content_type", contentType).
		Int("page", page).
		Int("page_size", pageSize).
		Str("sort_by", sortBy).
		Str("source_type", sourceType).
		Str("user_ksuid", userKSUID).
		Interface("tag_ids", tagIDs).
		Uint("category_id", categoryID).
		Str("level", level).
		Int64("total", total).
		Int("count", len(contents)).
		Msg("从数据库获取指定类型已发布内容列表成功")

	return contents, total, nil
}

// GetMyContents 获取用户自己的投稿内容，按时间倒序，支持分页和内容类型筛选
func (r *ContentRepository) GetMyContents(ctx context.Context, userKSUID string, page, pageSize int, contentType string) ([]*model.Content, int64, error) {
	log.Debug().
		Str("user_ksuid", userKSUID).
		Int("page", page).
		Int("page_size", pageSize).
		Str("content_type", contentType).
		Msg("开始从数据库获取用户投稿内容列表")

	var contents []*model.Content
	var total int64

	// 构建基础查询 - 获取用户的所有投稿（不限制状态）
	query := r.db.WithContext(ctx).Model(&model.Content{}).
		Where("user_ksuid = ?", userKSUID)

	// 如果指定了内容类型，添加类型筛选条件
	if contentType != "" {
		query = query.Where("type = ?", contentType)
	}

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Int("page", page).
			Int("page_size", pageSize).
			Str("content_type", contentType).
			Msg("获取用户投稿内容总数失败")
		return nil, 0, err
	}

	// 计算偏移量
	offset := (page - 1) * pageSize

	// 获取分页数据，按创建时间倒序排列（最新的在前）
	err = query.
		Select("user_ksuid, content_ksuid, title, cover_url, video_id, created_at, view_count, like_count, dislike_count, share_count, favorite_count, comment_count, complaint_count, duration, orientation, status, type").
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&contents).Error

	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Int("page", page).
			Int("page_size", pageSize).
			Str("content_type", contentType).
			Int64("total", total).
			Msg("获取用户投稿内容列表失败")
		return nil, 0, err
	}

	log.Debug().
		Str("user_ksuid", userKSUID).
		Int("page", page).
		Int("page_size", pageSize).
		Str("content_type", contentType).
		Int64("total", total).
		Int("count", len(contents)).
		Msg("从数据库获取用户投稿内容列表成功")

	return contents, total, nil
}

// buildOrderClause 根据排序类型构建ORDER BY子句
func (r *ContentRepository) buildOrderClause(sortBy string) string {
	switch sortBy {
	case "comprehensive":
		// 综合排序：综合考虑播放量、点赞数、收藏数、分享数、评论数和发布时间
		// 使用加权评分算法，最近发布的内容有时间加成
		return `(
			view_count * 0.3 +
			like_count * 0.25 +
			favorite_count * 0.25 +
			share_count * 0.15 +
			comment_count * 0.05 +
			CASE
				WHEN created_at > NOW() - INTERVAL '7 days' THEN 1000
				WHEN created_at > NOW() - INTERVAL '30 days' THEN 500
				ELSE 0
			END
		) DESC, created_at DESC`
	case "views":
		// 最多播放：按播放量降序，播放量相同时按发布时间降序
		return "view_count DESC, created_at DESC"
	case "likes":
		// 最多点赞：按点赞数降序，点赞数相同时按发布时间降序
		return "like_count DESC, created_at DESC"
	case "shares":
		// 最多转发：按分享数降序，分享数相同时按发布时间降序
		return "share_count DESC, created_at DESC"
	case "favorites":
		// 最多收藏：按收藏数降序，收藏数相同时按发布时间降序
		return "favorite_count DESC, created_at DESC"
	case "latest":
		fallthrough
	default:
		// 最新发布：按发布时间降序（默认排序）
		return "created_at DESC"
	}
}

// UpdateContentURLs 更新内容URL字段
func (r *ContentRepository) UpdateContentURLs(ctx context.Context, contentKSUID, playURL, keyFramesURL, previewURL string) error {
	log.Debug().
		Str("content_ksuid", contentKSUID).
		Str("play_url", playURL).
		Str("key_frames_url", keyFramesURL).
		Str("preview_url", previewURL).
		Msg("开始更新内容URL到数据库")

	updates := map[string]interface{}{}

	if playURL != "" {
		updates["play_url"] = playURL
	}
	if keyFramesURL != "" {
		updates["key_frames_url"] = keyFramesURL
	}
	if previewURL != "" {
		updates["preview_url"] = previewURL
	}

	if len(updates) == 0 {
		log.Debug().
			Str("content_ksuid", contentKSUID).
			Msg("没有URL需要更新")
		return nil
	}

	result := r.db.WithContext(ctx).
		Model(&model.Content{}).
		Where("content_ksuid = ?", contentKSUID).
		Updates(updates)

	if result.Error != nil {
		log.Error().
			Err(result.Error).
			Str("content_ksuid", contentKSUID).
			Msg("更新内容URL到数据库失败")
		return result.Error
	}

	log.Debug().
		Str("content_ksuid", contentKSUID).
		Int64("rows_affected", result.RowsAffected).
		Msg("更新内容URL到数据库成功")

	return nil
}

// AssociateContentWithTags 关联内容与标签
func (r *ContentRepository) AssociateContentWithTags(ctx context.Context, contentKSUID string, tags []*model.Tag) error {
	log.Debug().
		Str("content_ksuid", contentKSUID).
		Int("tag_count", len(tags)).
		Msg("开始关联内容与标签")

	// 先获取内容
	var content model.Content
	err := r.db.WithContext(ctx).Where("content_ksuid = ?", contentKSUID).First(&content).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("content_ksuid", contentKSUID).
			Msg("获取内容失败")
		return err
	}

	// 关联标签
	err = r.db.WithContext(ctx).Model(&content).Association("Tags").Append(tags)
	if err != nil {
		log.Error().
			Err(err).
			Str("content_ksuid", contentKSUID).
			Int("tag_count", len(tags)).
			Msg("关联内容与标签失败")
		return err
	}

	log.Debug().
		Str("content_ksuid", contentKSUID).
		Int("tag_count", len(tags)).
		Msg("关联内容与标签成功")

	return nil
}

// GetRandomVideos 随机获取视频，支持按标签和分类过滤
func (r *ContentRepository) GetRandomVideos(ctx context.Context, tagID, categoryID uint, level, orientation string, size int) ([]*model.Content, error) {
	log.Debug().
		Uint("tag_id", tagID).
		Uint("category_id", categoryID).
		Str("level", level).
		Str("orientation", orientation).
		Int("size", size).
		Msg("开始从数据库随机获取视频")

	var contents []*model.Content

	// 构建基础查询 - 只获取已发布的视频类型内容
	query := r.db.WithContext(ctx).Model(&model.Content{}).
		Where("status = ? AND type = ?", model.StatusPublished, model.TypeVideo)

	// 如果指定了分类ID，添加分类过滤条件
	if categoryID > 0 {
		query = query.Where("category_id = ?", categoryID)
	}

	// 如果指定了审核级别，添加级别过滤条件
	if level != "" {
		query = query.Where("level = ?", level)
	}

	// 如果指定了横竖屏方向，添加方向过滤条件
	if orientation != "" {
		query = query.Where("orientation = ?", orientation)
	}

	// 如果指定了标签ID，添加标签过滤条件
	if tagID > 0 {
		// 使用子查询来筛选包含指定标签的内容
		query = query.Where("content_ksuid IN (?)",
			r.db.Table("video_content_tags").
				Select("content_ksuid").
				Where("tag_id = ?", tagID))
	}

	// 使用数据库的随机排序功能获取随机结果
	// PostgreSQL使用RANDOM()，MySQL使用RAND()
	err := query.
		Select("user_ksuid, content_ksuid, title, cover_url, created_at, view_count, like_count, comment_count, favorite_count, video_id, duration, orientation, preview_video").
		Order("RANDOM()"). // PostgreSQL语法，如果使用MySQL需要改为RAND()
		Limit(size).
		Find(&contents).Error

	if err != nil {
		log.Error().
			Err(err).
			Uint("tag_id", tagID).
			Uint("category_id", categoryID).
			Str("level", level).
			Str("orientation", orientation).
			Int("size", size).
			Msg("随机获取视频失败")
		return nil, err
	}

	log.Debug().
		Uint("tag_id", tagID).
		Uint("category_id", categoryID).
		Str("level", level).
		Str("orientation", orientation).
		Int("size", size).
		Int("count", len(contents)).
		Msg("从数据库随机获取视频成功")

	return contents, nil
}

func (r *ContentRepository) UpdateContent(ctx context.Context, content *model.Content) error {
	return r.db.WithContext(ctx).Updates(content).Error
}

// GetContentByKSUID 根据KSUID获取内容（用于合集功能）
func (r *ContentRepository) GetContentByKSUID(ctx context.Context, contentKSUID string) (*model.Content, error) {
	log.Debug().
		Str("content_ksuid", contentKSUID).
		Msg("开始从数据库获取内容信息")

	var content model.Content
	err := r.db.WithContext(ctx).
		Where("content_ksuid = ?", contentKSUID).
		First(&content).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("content_ksuid", contentKSUID).
			Msg("从数据库获取内容信息失败")
		return nil, err
	}

	log.Debug().
		Str("content_ksuid", contentKSUID).
		Str("title", content.Title).
		Str("type", string(content.Type)).
		Str("status", string(content.Status)).
		Msg("从数据库获取内容信息成功")

	return &content, nil
}

// BatchGetContentsByKSUIDs 批量根据KSUID获取内容信息
func (r *ContentRepository) BatchGetContentsByKSUIDs(ctx context.Context, contentKSUIDs []string) ([]*model.Content, error) {
	if len(contentKSUIDs) == 0 {
		return []*model.Content{}, nil
	}

	log.Debug().
		Int("content_count", len(contentKSUIDs)).
		Msg("开始批量获取内容信息")

	var contents []*model.Content
	err := r.db.WithContext(ctx).
		Select("content_ksuid, title, description, cover_url, duration, view_count, like_count, comment_count, favorite_count, type, status").
		Where("content_ksuid IN ? AND status = ?", contentKSUIDs, model.StatusPublished).
		Find(&contents).Error

	if err != nil {
		log.Error().
			Err(err).
			Int("content_count", len(contentKSUIDs)).
			Msg("批量获取内容信息失败")
		return nil, err
	}

	log.Debug().
		Int("requested_count", len(contentKSUIDs)).
		Int("found_count", len(contents)).
		Msg("批量获取内容信息成功")

	return contents, nil
}
