package dto

import (
	"pxpat-backend/internal/content-cluster/video-service/model"
	"pxpat-backend/pkg/errors"
	"regexp"
	"strings"
	"time"
)

// UserBasicInfo 用户基本信息
type UserBasicInfo struct {
	UserKSUID   string `json:"user_ksuid"`   // 用户KSUID
	Username    string `json:"username"`     // 用户名
	Nickname    string `json:"nickname"`     // 用户昵称
	Avatar      string `json:"avatar"`       // 头像
	Bio         string `json:"bio"`          // 个人简介
	HideComment bool   `json:"hide_comment"` // 是否隐藏评论功能
}

// ExportContentModel 用于对外暴露的内容模型，只包含可以公开的字段
type ExportContentModel struct {
	// 基础信息
	ContentKSUID string              `json:"content_ksuid"`
	Title        string              `json:"title"`
	Description  string              `json:"description"`
	Type         model.ContentType   `json:"type"`
	Status       model.ContentStatus `json:"status"`

	// 视频编号
	VideoID string `json:"video_ID"`

	// 创作者信息
	UserKSUID string `json:"user_ksuid"`

	// 向外的播放信息
	CoverURL     string  `json:"cover_url"`
	PlayURL      string  `json:"play_url"`
	KeyFramesURL string  `json:"key_frames_url"`
	PreviewURL   string  `json:"preview_url"`
	Duration     float64 `json:"duration,omitempty"`
	Language     string  `json:"language"`
	Orientation  string  `json:"orientation,omitempty"`

	// 内容来源
	SourceType       model.ContentSourceType `json:"source_type"`
	OriginalURL      string                  `json:"original_url"`
	OriginalPlatform string                  `json:"original_platform"`
	OriginalIcon     string                  `json:"original_icon"`

	// 地区控制
	AllowedRegions    string `json:"allowed_regions,omitempty"`
	RestrictedRegions string `json:"restricted_regions,omitempty"`

	// 时间管理
	CreationTime time.Time `json:"creation_time"`

	// 统计信息
	ViewCount     int64 `json:"view_count"`
	LikeCount     int64 `json:"like_count"`
	DislikeCount  int64 `json:"dislike_count"`
	CommentCount  int64 `json:"comment_count"`
	FavoriteCount int64 `json:"favorite_count"`

	// 版权信息
	CopyrightInfo string    `json:"copyright_info,omitempty"`
	ExpiresAt     time.Time `json:"expires_at,omitempty"`

	// 审核
	AuditTaskID uint64 `json:"audit_task_id"`
	Level       string `json:"level"`

	// 关联关系
	Tags          []model.Tag          `json:"tags,omitempty"`
	Category      model.Category       `json:"category,omitempty"`
	Collaborators []model.Collaborator `json:"collaborators,omitempty"`

	// 基础字段
	CreatedAt time.Time `json:"created_at"`
}

func ContentToExportContentModel(content *model.Content) *ExportContentModel {
	return &ExportContentModel{
		ContentKSUID:      content.ContentKSUID,
		Title:             content.Title,
		Description:       content.Description,
		Type:              content.Type,
		Status:            content.Status,
		VideoID:           content.VideoID,
		UserKSUID:         content.UserKSUID,
		CoverURL:          content.CoverURL,
		PlayURL:           content.PlayURL,
		KeyFramesURL:      content.KeyFramesURL,
		PreviewURL:        content.PreviewURL,
		Duration:          content.Duration,
		Language:          content.Language,
		Orientation:       content.Orientation,
		SourceType:        content.SourceType,
		OriginalURL:       content.OriginalURL,
		OriginalPlatform:  content.OriginalPlatform,
		OriginalIcon:      content.OriginalIcon,
		AllowedRegions:    content.AllowedRegions,
		RestrictedRegions: content.RestrictedRegions,
		CreationTime:      content.CreationTime,
		ViewCount:         content.ViewCount,
		CommentCount:      content.CommentCount,
		CopyrightInfo:     content.CopyrightInfo,
		ExpiresAt:         content.ExpiresAt,
		AuditTaskID:       content.AuditTaskID,
		Level:             content.Level,
		Tags:              content.Tags,
		Category:          content.Category,
		Collaborators:     content.Collaborators,
		CreatedAt:         content.CreatedAt,
	}
}

// CollaboratorWithUserInfo 包含用户信息的协作者
type CollaboratorWithUserInfo struct {
	*model.Collaborator                // 嵌入原始协作者信息
	UserInfo            *UserBasicInfo `json:"user_info,omitempty"` // 用户基本信息
}

// ContentDetailWithUserInfoAndCollaborators 未登录时候获取的视频详情
type ContentDetailWithUserInfoAndCollaborators struct {
	*ExportContentModel                             // 嵌入原始内容
	UserInfo            *UserBasicInfo              `json:"user_info,omitempty"`     // 内容发布者用户信息
	Collaborators       []*CollaboratorWithUserInfo `json:"collaborators,omitempty"` // 协作者列表（包含用户信息）
}

// ContentDetailResponse  登录后获取的内容详情
type ContentDetailResponse struct {
	*ExportContentModel                               // 嵌入内容基础信息
	UserInteractionStatus                             // 用户交互状态（仅在用户登录时返回）
	UserInfo              *UserBasicInfo              `json:"user_info"`     // 内容发布者用户信息
	Collaborators         []*CollaboratorWithUserInfo `json:"collaborators"` // 协作者列表（包含用户信息）
}

// UserInteractionStatus 用户交互状态
type UserInteractionStatus struct {
	IsLiked     bool `json:"is_liked" default:"false"`     // 是否已点赞
	IsDisliked  bool `json:"is_disliked" default:"false"`  // 是否已不喜欢
	IsFavorited bool `json:"is_favorited" default:"false"` // 是否已收藏
}

type UpdateContentStatusRequest struct {
	ContentKSUID string `json:"content_ksuid"`
	Status       string `json:"status"`
}

// ContentSortType 内容排序类型枚举
type ContentSortType string

const (
	SortByComprehensive ContentSortType = "comprehensive" // 综合排序
	SortByViews         ContentSortType = "views"         // 最多播放
	SortByLatest        ContentSortType = "latest"        // 最新发布
	SortByFavorites     ContentSortType = "favorites"     // 最多收藏
	SortByLikes         ContentSortType = "likes"         // 最多点赞
	SortByShares        ContentSortType = "shares"        // 最多转发
)

// ContentSimpleModel 内容摘要DTO，只包含必要字段
type ContentSimpleModel struct {
	UserKSUID      string         `json:"user_ksuid"`              // 发布用户ID
	ContentKSUID   string         `json:"content_ksuid"`           // 内容ID
	ContentType    string         `json:"content_type"`            // 内容类型
	Title          string         `json:"title"`                   // 标题
	VideoID        string         `json:"video_id"`                // 视频编号
	CoverURL       string         `json:"cover_url"`               // 封面URL
	CreatedAt      string         `json:"created_at"`              // 创建时间
	ViewCount      int64          `json:"view_count"`              // 浏览量
	LikeCount      int64          `json:"like_count"`              // 点赞数
	DislikeCount   int64          `json:"dislike_count"`           // 不喜欢数
	RewardCount    int64          `json:"reward_count"`            // 打赏数
	CommentCount   int64          `json:"comment_count"`           // 评论数
	FavoriteCount  int64          `json:"favorite_count"`          // 收藏数
	ShareCount     int64          `json:"share_count"`             // 分享数
	ComplaintCount int64          `json:"complaint_count"`         // 投诉数
	Duration       float64        `json:"duration"`                // 时长
	Orientation    string         `json:"orientation"`             // 视频方向
	PreviewVideo   string         `json:"preview_video,omitempty"` // 预览视频
	UserInfo       *UserBasicInfo `json:"user_info,omitempty"`     // 用户信息
}

// ConvertToContentSimpleModel 将model.Content转换为ContentSummaryDTO
func ConvertToContentSimpleModel(content *model.Content) *ContentSimpleModel {
	return &ContentSimpleModel{
		UserKSUID:      content.UserKSUID,
		ContentKSUID:   content.ContentKSUID,
		ContentType:    string(content.Type),
		Title:          content.Title,
		VideoID:        content.VideoID,
		CoverURL:       content.CoverURL,
		CreatedAt:      content.CreatedAt.Format("2006-01-02 15:04:05"),
		ViewCount:      content.ViewCount,
		CommentCount:   content.CommentCount,
		ComplaintCount: content.ComplaintCount,
		Duration:       content.Duration,
		Orientation:    content.Orientation,
		PreviewVideo:   content.PreviewURL,
	}
}

func ConvertToContentSimpleModels(content []*model.Content) []*ContentSimpleModel {
	// 批量转换Content到ContentSimpleModel
	result := make([]*ContentSimpleModel, 0, len(content))
	for _, c := range content {
		result = append(result, ConvertToContentSimpleModel(c))
	}
	return result
}

// GetPublishedContentsByTypeRequest 按类型获取已发布内容请求
type GetPublishedContentsByTypeRequest struct {
	Type       string          `form:"type" json:"type" binding:"required"` // 内容类型，必填
	Page       int             `form:"page" json:"page"`                    // 页码，从1开始
	PageSize   int             `form:"page_size" json:"page_size"`          // 每页数量，默认20
	SortBy     ContentSortType `form:"sort_by" json:"sort_by"`              // 排序方式，默认为latest
	SourceType string          `form:"source_type" json:"source_type"`      // 内容来源类型筛选：original(原创)/transshipment(分享)，可选
	UserKSUID  string          `form:"user_ksuid" json:"user_ksuid"`        // 投稿人用户ID筛选，可选
	TagIDs     []uint          `form:"tag_ids" json:"tag_ids"`              // 标签ID列表筛选，可选
	CategoryID uint            `form:"category_id" json:"category_id"`      // 视频分类ID筛选，可选
	Level      string          `form:"level" json:"level"`                  // 审核级别筛选：A/B/C，可选，不填默认不筛选
}

// GetPublishedContentsByTypeResponse 按类型获取已发布内容响应
type GetPublishedContentsByTypeResponse struct {
	Contents   []*ContentSimpleModel `json:"contents"`    // 内容列表
	Total      int64                 `json:"total"`       // 总数量
	Page       int                   `json:"page"`        // 当前页码
	PageSize   int                   `json:"page_size"`   // 每页数量
	TotalPages int                   `json:"total_pages"` // 总页数
	HasNext    bool                  `json:"has_next"`    // 是否有下一页
	HasPrev    bool                  `json:"has_prev"`    // 是否有上一页
}

// GetRandomVideosRequest 随机获取视频请求
type GetRandomVideosRequest struct {
	TagID       uint   `form:"tag_id" json:"tag_id"`                // 标签ID，0表示不限制标签
	CategoryID  uint   `form:"category_id" json:"category_id"`      // 分类ID，0表示不限制分类
	Level       string `form:"level" json:"level"`                  // 审核级别筛选：A/B/C，可选，不填默认不筛选
	Orientation string `form:"orientation" json:"orientation"`      // 横竖屏筛选：landscape(横屏)/portrait(竖屏)，可选，不填默认不筛选
	Size        int    `form:"size" json:"size" binding:"required"` // 获取数量，默认20，最大100
}

// GetRandomVideosResponse 随机获取视频响应
type GetRandomVideosResponse struct {
	Videos []*ContentSimpleModel `json:"videos"` // 视频列表
	Count  int                   `json:"count"`  // 视频数量
}

// GetMyContentsRequest 获取自己投稿请求
type GetMyContentsRequest struct {
	Page        int    `form:"page" json:"page" example:"1"`                                                       // 页码，从1开始
	PageSize    int    `form:"page_size" json:"page_size" example:"20"`                                            // 每页数量，默认20
	ContentType string `form:"content_type" json:"content_type" binding:"omitempty,oneof=video anime short_video"` // 内容类型过滤：video/anime/short_video，为空则获取所有
}

// GetMyContentsResponse 获取自己投稿响应
type GetMyContentsResponse struct {
	Contents   []*ContentSimpleModel `json:"contents"`    // 内容列表
	Total      int64                 `json:"total"`       // 总数量
	Page       int                   `json:"page"`        // 当前页码
	PageSize   int                   `json:"page_size"`   // 每页数量
	TotalPages int                   `json:"total_pages"` // 总页数
	HasNext    bool                  `json:"has_next"`    // 是否有下一页
	HasPrev    bool                  `json:"has_prev"`    // 是否有上一页
}

type PublishVideoRequest struct {
	Title             string                 `json:"title" binding:"required,min=5,max=80"`
	Bio               string                 `json:"bio" binding:"max=500"`
	Category          uint                   `json:"category" binding:"required,gt=5"`
	Type              string                 `json:"type" binding:"required,oneof=video anime short"` // 内容类型 video/anime/short
	OriginalTags      string                 `json:"original_tags"`                                   // 原始标签（格式：标签1,标签2,标签3）
	VideoID           string                 `json:"video_id" binding:"required,min=3,max=10"`
	FileKSUID         string                 `json:"file_ksuid" binding:"required"`
	SourceType        string                 `json:"source_type" binding:"required,oneof=original transshipment"` // 内容来源类型
	CoverKSUID        string                 `json:"cover_ksuid" binding:"required"`                              // 封面KSUID（必需，从存储服务获取封面信息）
	Collaborators     []CollaboratorBaseInfo `json:"collaborators"`                                               // 协作者列表
	PublishAt         time.Time              `json:"publish_at" binding:"required"`
	AutoPushAudit     bool                   `json:"auto_push_audit" binding:"required"` // 是否自动推送到审核
	TempCreationTime  string                 `json:"creation_time" binding:"required,date"`
	CreationTime      time.Time              `json:"-"` // 创作日期
	SplitMainCreators []string
	SplitMembers      []string
}

func ValidatePublishVideoRequest(req *PublishVideoRequest) *errors.Errors {
	// 使用正则表达式验证格式：英文字母-数字
	matched, err := regexp.MatchString(`^[a-zA-Z]+-[0-9]+$`, req.VideoID)
	if err != nil || !matched {
		return errors.NewGlobalErrors(errors.VIDEO_ID_FORMAT_ERROR, errors.VIDEO_ID_FORMAT_ERROR, errors.NoneError)
	}
	// 校验原始标签格式
	if req.OriginalTags != "" {
		// 使用正则表达式检查格式是否为 "标签1,标签2,标签3"
		// 正则说明：^[^,\s]+(\s*,\s*[^,\s]+)*$
		// ^ 开始，[^,\s]+ 非逗号非空格的字符至少一个，(\s*,\s*[^,\s]+)* 零个或多个(可选空格+逗号+可选空格+非逗号非空格字符)，$ 结束
		matched, err := regexp.MatchString(`^[^,\s]+(\s*,\s*[^,\s]+)*$`, req.OriginalTags)
		if err != nil || !matched {
			return errors.NewGlobalErrors(errors.ORIGINAL_TAGS_ERROR, errors.ORIGINAL_TAGS_ERROR, errors.NoneError)
		}
		// 检查每个标签的长度
		tags := strings.Split(req.OriginalTags, ",")
		for _, tag := range tags {
			trimmedTag := strings.TrimSpace(tag)
			if len(trimmedTag) > 16 {
				return errors.NewGlobalErrors(errors.ORIGINAL_TAGS_ERROR, errors.ORIGINAL_TAGS_ERROR, errors.NoneError)
			}
		}
	}
	//if req.PublishAt.IsZero() {
	//	return errors.NewGlobalErrors(errors.PUBLISH_AT_ERROR, errors.PUBLISH_AT_ERROR, errors.NoneError)
	//}
	// 创作时间
	req.CreationTime, err = time.Parse("2006-01-02", req.TempCreationTime)

	// 如果都不为空，返回 nil 表示验证通过
	return nil
}

// PublishAnimeRequest 发布动漫请求结构体
type PublishAnimeRequest struct {
	Title             string                 `json:"title"`
	Bio               string                 `json:"bio"`
	Category          uint                   `json:"category"`
	OriginalTags      string                 `json:"original_tags"` // 原始标签（格式：标签1,标签2,标签3）
	VideoID           string                 `json:"video_id"`
	FileKSUID         string                 `json:"file_ksuid"`
	SourceType        string                 `json:"source_type"`   // 内容来源类型
	CoverKSUID        string                 `json:"cover_ksuid"`   // 封面KSUID（必需，从存储服务获取封面信息）
	Collaborators     []CollaboratorBaseInfo `json:"collaborators"` // 协作者列表
	PublishAt         time.Time              `json:"publish_at"`
	CreationTime      time.Time              `json:"-"`               // 创作日期
	AutoPushAudit     bool                   `json:"auto_push_audit"` // 是否自动推送到审核
	TempCreationTime  string                 `json:"creation_time"`
	SplitMainCreators []string
	SplitMembers      []string
}

// ValidatePublishAnimeRequest 验证发布动漫请求参数
func ValidatePublishAnimeRequest(req *PublishAnimeRequest) *errors.Errors {
	// 检查字段是否为空
	if strings.TrimSpace(req.Title) == "" {
		return errors.NewGlobalErrors(errors.TITLE_ERROR, errors.TITLE_ERROR, errors.NoneError)
	}
	// 检查标题长度
	if len(strings.TrimSpace(req.Title)) > 80 {
		return errors.NewGlobalErrors(errors.TITLE_ERROR, errors.TITLE_ERROR, errors.NoneError)
	}
	// 检查描述长度
	if len(strings.TrimSpace(req.Bio)) > 500 {
		return errors.NewGlobalErrors(errors.INVALID_PARAMETER, errors.INVALID_PARAMETER, errors.NoneError)
	}
	if req.Category < 0 {
		return errors.NewGlobalErrors(errors.CATEGORY_ERROR, errors.CATEGORY_ERROR, errors.NoneError)
	}
	if strings.TrimSpace(req.VideoID) == "" {
		return errors.NewGlobalErrors(errors.VIDEO_ID_ERROR, errors.VIDEO_ID_ERROR, errors.NoneError)
	}
	// 验证VideoID格式：必须为[英文-数字]且总长度≤10
	videoID := strings.TrimSpace(req.VideoID)
	if len(videoID) > 10 {
		return errors.NewGlobalErrors(errors.VIDEO_ID_FORMAT_ERROR, errors.VIDEO_ID_FORMAT_ERROR, errors.NoneError)
	}
	// 使用正则表达式验证格式：英文字母-数字
	matched, err := regexp.MatchString(`^[a-zA-Z]+-[0-9]+$`, videoID)
	if err != nil || !matched {
		return errors.NewGlobalErrors(errors.VIDEO_ID_FORMAT_ERROR, errors.VIDEO_ID_FORMAT_ERROR, errors.NoneError)
	}
	if strings.TrimSpace(req.FileKSUID) == "" {
		return errors.NewGlobalErrors(errors.FILE_ID_ERROR, errors.FILE_ID_ERROR, errors.NoneError)
	}
	// 封面验证：cover_ksuid 是必需的
	if strings.TrimSpace(req.CoverKSUID) == "" {
		return errors.NewGlobalErrors(errors.COVER_ERROR, errors.COVER_ERROR, errors.NoneError)
	}
	// 校验原始标签格式
	if req.OriginalTags != "" {
		// 使用正则表达式检查格式是否为 "标签1,标签2,标签3"
		// 正则说明：^[^,\s]+(\s*,\s*[^,\s]+)*$
		// ^ 开始，[^,\s]+ 非逗号非空格的字符至少一个，(\s*,\s*[^,\s]+)* 零个或多个(可选空格+逗号+可选空格+非逗号非空格字符)，$ 结束
		matched, err := regexp.MatchString(`^[^,\s]+(\s*,\s*[^,\s]+)*$`, req.OriginalTags)
		if err != nil || !matched {
			return errors.NewGlobalErrors(errors.ORIGINAL_TAGS_ERROR, errors.ORIGINAL_TAGS_ERROR, errors.NoneError)
		}
		// 检查每个标签的长度
		tags := strings.Split(req.OriginalTags, ",")
		for _, tag := range tags {
			trimmedTag := strings.TrimSpace(tag)
			if len(trimmedTag) > 16 {
				return errors.NewGlobalErrors(errors.ORIGINAL_TAGS_ERROR, errors.ORIGINAL_TAGS_ERROR, errors.NoneError)
			}
		}
	}
	// 创作时间
	parsedTime, err := time.Parse("2006-01-02", req.TempCreationTime)
	if err != nil {
		return errors.NewGlobalErrors(errors.CREATED_TIME_ERROR, errors.FILE_ID_ERROR, errors.NoneError)
	}
	req.CreationTime = parsedTime

	// 如果都不为空，返回 nil 表示验证通过
	return nil
}
