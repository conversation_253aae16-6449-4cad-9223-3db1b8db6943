{"openapi": "3.0.3", "info": {"title": "Interaction Service External API", "description": "External API for user interactions including albums, favorites, likes, and play history", "version": "1.0.0", "contact": {"name": "PxPat Backend Team"}}, "servers": [{"url": "http://localhost:12009/api/v1", "description": "Development server"}], "security": [{"BearerAuth": []}], "components": {"securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "schemas": {"GlobalResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "Response code"}, "message": {"type": "string", "description": "Response message"}, "data": {"description": "Response data"}}}, "ErrorResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "Error code"}, "message": {"type": "string", "description": "Error message"}}}}}, "paths": {"/albums": {"post": {"tags": ["Albums"], "summary": "Create album", "description": "Create a new album", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "cover_url": {"type": "string"}}, "required": ["title"]}}}}, "responses": {"200": {"description": "Album created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/albums/{album_ksuid}": {"get": {"tags": ["Albums"], "summary": "Get album", "description": "Get album information", "parameters": [{"name": "album_ksuid", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Album KSUID"}], "responses": {"200": {"description": "Album information", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}, "put": {"tags": ["Albums"], "summary": "Update album", "description": "Update album information", "security": [{"BearerAuth": []}], "parameters": [{"name": "album_ksuid", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Album KSUID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "cover_url": {"type": "string"}}}}}}, "responses": {"200": {"description": "Album updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}, "delete": {"tags": ["Albums"], "summary": "Delete album", "description": "Delete an album", "security": [{"BearerAuth": []}], "parameters": [{"name": "album_ksuid", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Album KSUID"}], "responses": {"200": {"description": "Album deleted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}}, "/albums/{album_ksuid}/contents": {"get": {"tags": ["Albums"], "summary": "Get album contents", "description": "Get contents in an album", "parameters": [{"name": "album_ksuid", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Album KSUID"}, {"name": "page", "in": "query", "schema": {"type": "integer", "default": 1}, "description": "Page number"}, {"name": "page_size", "in": "query", "schema": {"type": "integer", "default": 20}, "description": "Page size"}], "responses": {"200": {"description": "Album contents", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}}, "/albums/user/{user_ksuid}": {"get": {"tags": ["Albums"], "summary": "Get user albums", "description": "Get albums created by a user", "parameters": [{"name": "user_ksuid", "in": "path", "required": true, "schema": {"type": "string"}, "description": "User KSUID"}, {"name": "page", "in": "query", "schema": {"type": "integer", "default": 1}, "description": "Page number"}, {"name": "page_size", "in": "query", "schema": {"type": "integer", "default": 20}, "description": "Page size"}], "responses": {"200": {"description": "User albums", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}}, "/albums/contents": {"post": {"tags": ["Albums"], "summary": "Add content to album", "description": "Add content to an album", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"album_ksuid": {"type": "string"}, "content_ksuid": {"type": "string"}, "content_type": {"type": "string", "enum": ["video", "anime", "short"]}}, "required": ["album_ksuid", "content_ksuid", "content_type"]}}}}, "responses": {"200": {"description": "Content added successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}, "delete": {"tags": ["Albums"], "summary": "Remove content from album", "description": "Remove content from an album", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"album_ksuid": {"type": "string"}, "content_ksuid": {"type": "string"}}, "required": ["album_ksuid", "content_ksuid"]}}}}, "responses": {"200": {"description": "Content removed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}}, "/albums/contents/batch-add": {"post": {"tags": ["Albums"], "summary": "Batch add contents to album", "description": "Add multiple contents to an album", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"album_ksuid": {"type": "string"}, "contents": {"type": "array", "items": {"type": "object", "properties": {"content_ksuid": {"type": "string"}, "content_type": {"type": "string", "enum": ["video", "anime", "short"]}}}}}, "required": ["album_ksuid", "contents"]}}}}, "responses": {"200": {"description": "Contents added successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}}, "/albums/contents/batch-remove": {"post": {"tags": ["Albums"], "summary": "Batch remove contents from album", "description": "Remove multiple contents from an album", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"album_ksuid": {"type": "string"}, "content_ksuids": {"type": "array", "items": {"type": "string"}}}, "required": ["album_ksuid", "content_ksuids"]}}}}, "responses": {"200": {"description": "Contents removed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}}, "/favorites/folders": {"get": {"tags": ["Favorites"], "summary": "Get favorite folders", "description": "Get user's favorite folders", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Favorite folders", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}, "post": {"tags": ["Favorites"], "summary": "Create favorite folder", "description": "Create a new favorite folder", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"dir_name": {"type": "string"}, "description": {"type": "string"}}, "required": ["dir_name"]}}}}, "responses": {"200": {"description": "Folder created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}}, "/favorites/folders/{folder_id}": {"get": {"tags": ["Favorites"], "summary": "Get favorite folder", "description": "Get favorite folder details", "security": [{"BearerAuth": []}], "parameters": [{"name": "folder_id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Folder ID"}], "responses": {"200": {"description": "Folder details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}, "put": {"tags": ["Favorites"], "summary": "Update favorite folder", "description": "Update favorite folder information", "security": [{"BearerAuth": []}], "parameters": [{"name": "folder_id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Folder ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"dir_name": {"type": "string"}, "description": {"type": "string"}}}}}}, "responses": {"200": {"description": "Folder updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}, "delete": {"tags": ["Favorites"], "summary": "Delete favorite folder", "description": "Delete a favorite folder", "security": [{"BearerAuth": []}], "parameters": [{"name": "folder_id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Folder ID"}], "responses": {"200": {"description": "Folder deleted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}}, "/favorites/items": {"get": {"tags": ["Favorites"], "summary": "Get favorite items", "description": "Get favorite items in a folder", "security": [{"BearerAuth": []}], "parameters": [{"name": "folder_id", "in": "query", "schema": {"type": "string"}, "description": "Folder ID"}, {"name": "page", "in": "query", "schema": {"type": "integer", "default": 1}, "description": "Page number"}, {"name": "page_size", "in": "query", "schema": {"type": "integer", "default": 20}, "description": "Page size"}], "responses": {"200": {"description": "Favorite items", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}, "post": {"tags": ["Favorites"], "summary": "Add to favorite", "description": "Add content to favorite folder", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"content_ksuid": {"type": "string"}, "content_type": {"type": "string", "enum": ["video", "anime", "short"]}, "folder_id": {"type": "string"}}, "required": ["content_ksuid", "content_type"]}}}}, "responses": {"200": {"description": "Added to favorite successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}, "delete": {"tags": ["Favorites"], "summary": "Remove from favorite", "description": "Remove content from favorite folder", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"content_ksuid": {"type": "string"}, "folder_id": {"type": "string"}}, "required": ["content_ksuid"]}}}}, "responses": {"200": {"description": "Removed from favorite successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}}, "/favorites/items/move": {"put": {"tags": ["Favorites"], "summary": "Move favorite item", "description": "Move favorite item to another folder", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"content_ksuid": {"type": "string"}, "from_folder_id": {"type": "string"}, "to_folder_id": {"type": "string"}}, "required": ["content_ksuid", "to_folder_id"]}}}}, "responses": {"200": {"description": "<PERSON><PERSON> moved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}}, "/favorites/items/status": {"get": {"tags": ["Favorites"], "summary": "Check favorite status", "description": "Check if content is favorited", "security": [{"BearerAuth": []}], "parameters": [{"name": "content_ksuid", "in": "query", "required": true, "schema": {"type": "string"}, "description": "Content KSUID"}], "responses": {"200": {"description": "Favorite status", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}}, "/favorites/stats": {"get": {"tags": ["Favorites"], "summary": "Get favorite stats", "description": "Get user's favorite statistics", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Favorite statistics", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}}, "/like/content": {"post": {"tags": ["<PERSON>s"], "summary": "Like/Unlike content", "description": "Like, dislike, or cancel like/dislike for content", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"content_ksuid": {"type": "string"}, "content_type": {"type": "string", "enum": ["video", "anime", "short"]}, "action": {"type": "string", "enum": ["like", "dislike", "cancel"]}}, "required": ["content_ksuid", "content_type", "action"]}}}}, "responses": {"200": {"description": "Action completed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}}, "/like/content/stats": {"get": {"tags": ["<PERSON>s"], "summary": "Get content like stats", "description": "Get like statistics for content", "parameters": [{"name": "content_ksuid", "in": "query", "required": true, "schema": {"type": "string"}, "description": "Content KSUID"}], "responses": {"200": {"description": "Content like statistics", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}}, "/like/status": {"get": {"tags": ["<PERSON>s"], "summary": "Get user like status", "description": "Get user's like status for content", "parameters": [{"name": "user_ksuid", "in": "query", "required": true, "schema": {"type": "string"}, "description": "User KSUID"}, {"name": "content_ksuid", "in": "query", "required": true, "schema": {"type": "string"}, "description": "Content KSUID"}], "responses": {"200": {"description": "User like status", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}}, "/like/status/batch": {"post": {"tags": ["<PERSON>s"], "summary": "Batch get user like status", "description": "Get user's like status for multiple contents", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"user_ksuid": {"type": "string"}, "content_ksuids": {"type": "array", "items": {"type": "string"}}}, "required": ["user_ksuid", "content_ksuids"]}}}}, "responses": {"200": {"description": "Batch like status", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}}, "/like/my": {"get": {"tags": ["<PERSON>s"], "summary": "Get my likes", "description": "Get user's like records", "security": [{"BearerAuth": []}], "parameters": [{"name": "content_type", "in": "query", "schema": {"type": "string", "enum": ["video", "anime", "short"]}, "description": "Content type filter"}, {"name": "like_type", "in": "query", "schema": {"type": "string", "enum": ["like", "dislike"]}, "description": "Like type filter"}, {"name": "page", "in": "query", "schema": {"type": "integer", "default": 1}, "description": "Page number"}, {"name": "page_size", "in": "query", "schema": {"type": "integer", "default": 20}, "description": "Page size"}], "responses": {"200": {"description": "User like records", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}}, "/like/user": {"get": {"tags": ["<PERSON>s"], "summary": "Get other user likes", "description": "Get other user's like records", "security": [{"BearerAuth": []}], "parameters": [{"name": "user_ksuid", "in": "query", "required": true, "schema": {"type": "string"}, "description": "Target user KSUID"}, {"name": "content_type", "in": "query", "schema": {"type": "string", "enum": ["video", "anime", "short"]}, "description": "Content type filter"}, {"name": "like_type", "in": "query", "schema": {"type": "string", "enum": ["like", "dislike"]}, "description": "Like type filter"}, {"name": "page", "in": "query", "schema": {"type": "integer", "default": 1}, "description": "Page number"}, {"name": "page_size", "in": "query", "schema": {"type": "integer", "default": 20}, "description": "Page size"}], "responses": {"200": {"description": "User like records", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}}, "/play-history": {"post": {"tags": ["Play History"], "summary": "Update play history", "description": "Update user's play history record", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"content_ksuid": {"type": "string"}, "content_type": {"type": "string", "enum": ["video", "anime", "short"]}, "play_duration": {"type": "integer", "description": "Play duration in seconds"}}, "required": ["content_ksuid", "content_type", "play_duration"]}}}}, "responses": {"200": {"description": "Play history updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}, "delete": {"tags": ["Play History"], "summary": "Delete play history", "description": "Delete specific play history records", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"play_history_item_ids": {"type": "array", "items": {"type": "string"}}}, "required": ["play_history_item_ids"]}}}}, "responses": {"200": {"description": "Play history deleted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}}, "/play-history/my": {"get": {"tags": ["Play History"], "summary": "Get my play history", "description": "Get user's play history records", "security": [{"BearerAuth": []}], "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "default": 1}, "description": "Page number"}, {"name": "page_size", "in": "query", "schema": {"type": "integer", "default": 20}, "description": "Page size"}, {"name": "content_type", "in": "query", "schema": {"type": "string", "enum": ["video", "anime", "short"]}, "description": "Content type filter"}], "responses": {"200": {"description": "User play history records", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}}, "/play-history/all": {"delete": {"tags": ["Play History"], "summary": "Clear all play history", "description": "Clear all user's play history records", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "All play history cleared successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}}}}