package service

import (
	"context"
	"fmt"
	"pxpat-backend/internal/user-cluster/user-service/dto"
	"pxpat-backend/internal/user-cluster/user-service/model"
	"pxpat-backend/internal/user-cluster/user-service/repository"
	"pxpat-backend/pkg/errors"

	"github.com/rs/zerolog/log"
)

// FollowService 关注服务
type FollowService struct {
	followRepo repository.FollowRepository
	userRepo   repository.UserRepository
}

// NewFollowService 创建关注服务实例
func NewFollowService(followRepo repository.FollowRepository, userRepo repository.UserRepository) *FollowService {
	return &FollowService{
		followRepo: followRepo,
		userRepo:   userRepo,
	}
}

// FollowUser 关注用户
func (s *FollowService) FollowUser(ctx context.Context, followerKSUID, followeeKSUID string) (*dto.FollowUserResponse, error) {
	log.Info().
		Str("follower_ksuid", followerKSUID).
		Str("followee_ksuid", followeeKSUID).
		Msg("开始处理关注用户请求")

	// 验证输入参数
	if followerKSUID == "" {
		log.Error().Msg("关注者用户ID为空")
		return nil, errors.New(errors.INVALID_PARAMETER, "关注者用户ID不能为空")
	}

	if followeeKSUID == "" {
		log.Error().Msg("被关注者用户ID为空")
		return nil, errors.New(errors.INVALID_PARAMETER, "被关注者用户ID不能为空")
	}

	// 检查是否尝试关注自己
	if followerKSUID == followeeKSUID {
		log.Warn().Str("user_ksuid", followerKSUID).Msg("用户尝试关注自己")
		return nil, errors.New(errors.INVALID_PARAMETER, "不能关注自己")
	}

	// 检查被关注的用户是否存在
	followee, err := s.userRepo.GetByUserKSUID(ctx, followeeKSUID)
	if err != nil {
		if err == repository.ErrUserNotFound {
			return &dto.FollowUserResponse{
				Success:       false,
				Message:       "用户不存在",
				FolloweeKSUID: followeeKSUID,
				IsFollowing:   false,
			}, nil
		}
		log.Error().Err(err).Str("followee_ksuid", followeeKSUID).Msg("获取被关注用户信息失败")
		return nil, errors.New(errors.INTERNAL_ERROR, "获取用户信息失败")
	}

	// 检查是否已经关注
	isFollowing, err := s.followRepo.IsFollowing(ctx, followerKSUID, followeeKSUID)
	if err != nil {
		log.Error().Err(err).Str("follower_ksuid", followerKSUID).Str("followee_ksuid", followeeKSUID).Msg("检查关注状态失败")
		return nil, errors.New(errors.INTERNAL_ERROR, "检查关注状态失败")
	}

	if isFollowing {
		return &dto.FollowUserResponse{
			Success:       false,
			Message:       "已经关注了该用户",
			FolloweeKSUID: followeeKSUID,
			IsFollowing:   true,
		}, nil
	}

	// 创建关注关系
	follow := model.NewUserFollow(followerKSUID, followeeKSUID)
	err = s.followRepo.CreateFollow(ctx, follow)
	if err != nil {
		log.Error().Err(err).Str("follower_ksuid", followerKSUID).Str("followee_ksuid", followeeKSUID).Msg("创建关注关系失败")
		return nil, errors.New(errors.INTERNAL_ERROR, "关注失败")
	}

	// 更新关注者的关注数量
	err = s.followRepo.UpdateUserFollowCounts(ctx, followerKSUID)
	if err != nil {
		log.Error().Err(err).Str("follower_ksuid", followerKSUID).Msg("更新关注者关注数量失败")
	}

	// 更新被关注者的粉丝数量
	err = s.followRepo.UpdateUserFollowCounts(ctx, followeeKSUID)
	if err != nil {
		log.Error().Err(err).Str("followee_ksuid", followeeKSUID).Msg("更新被关注者粉丝数量失败")
	}

	log.Info().
		Str("follower_ksuid", followerKSUID).
		Str("followee_ksuid", followeeKSUID).
		Str("followee_nickname", followee.Nickname).
		Msg("关注用户成功")

	return &dto.FollowUserResponse{
		Success:       true,
		Message:       fmt.Sprintf("成功关注 %s", followee.Nickname),
		FolloweeKSUID: followeeKSUID,
		IsFollowing:   true,
	}, nil
}

// UnfollowUser 取消关注用户
func (s *FollowService) UnfollowUser(ctx context.Context, followerKSUID, followeeKSUID string) (*dto.FollowUserResponse, error) {
	log.Info().
		Str("follower_ksuid", followerKSUID).
		Str("followee_ksuid", followeeKSUID).
		Msg("开始处理取消关注用户请求")

	// 验证输入参数
	if followerKSUID == "" {
		log.Error().Msg("关注者用户ID为空")
		return nil, errors.New(errors.INVALID_PARAMETER, "关注者用户ID不能为空")
	}

	if followeeKSUID == "" {
		log.Error().Msg("被关注者用户ID为空")
		return nil, errors.New(errors.INVALID_PARAMETER, "被关注者用户ID不能为空")
	}

	// 检查是否尝试取消关注自己
	if followerKSUID == followeeKSUID {
		log.Warn().Str("user_ksuid", followerKSUID).Msg("用户尝试取消关注自己")
		return nil, errors.New(errors.INVALID_PARAMETER, "不能取消关注自己")
	}

	// 检查是否已经关注
	isFollowing, err := s.followRepo.IsFollowing(ctx, followerKSUID, followeeKSUID)
	if err != nil {
		log.Error().Err(err).Str("follower_ksuid", followerKSUID).Str("followee_ksuid", followeeKSUID).Msg("检查关注状态失败")
		return nil, errors.New(errors.INTERNAL_ERROR, "检查关注状态失败")
	}

	if !isFollowing {
		return &dto.FollowUserResponse{
			Success:       false,
			Message:       "未关注该用户",
			FolloweeKSUID: followeeKSUID,
			IsFollowing:   false,
		}, nil
	}

	// 删除关注关系
	err = s.followRepo.DeleteFollow(ctx, followerKSUID, followeeKSUID)
	if err != nil {
		log.Error().Err(err).Str("follower_ksuid", followerKSUID).Str("followee_ksuid", followeeKSUID).Msg("删除关注关系失败")
		return nil, errors.New(errors.INTERNAL_ERROR, "取消关注失败")
	}

	// 更新关注者的关注数量
	err = s.followRepo.UpdateUserFollowCounts(ctx, followerKSUID)
	if err != nil {
		log.Error().Err(err).Str("follower_ksuid", followerKSUID).Msg("更新关注者关注数量失败")
	}

	// 更新被关注者的粉丝数量
	err = s.followRepo.UpdateUserFollowCounts(ctx, followeeKSUID)
	if err != nil {
		log.Error().Err(err).Str("followee_ksuid", followeeKSUID).Msg("更新被关注者粉丝数量失败")
	}

	log.Info().
		Str("follower_ksuid", followerKSUID).
		Str("followee_ksuid", followeeKSUID).
		Msg("取消关注用户成功")

	return &dto.FollowUserResponse{
		Success:       true,
		Message:       "取消关注成功",
		FolloweeKSUID: followeeKSUID,
		IsFollowing:   false,
	}, nil
}

// GetFollowers 获取粉丝列表
func (s *FollowService) GetFollowers(ctx context.Context, userKSUID string, page, pageSize int) (*dto.FollowListResponse, error) {
	log.Debug().
		Str("user_ksuid", userKSUID).
		Int("page", page).
		Int("page_size", pageSize).
		Msg("开始获取粉丝列表")

	// 验证输入参数
	if userKSUID == "" {
		log.Error().Msg("用户ID为空")
		return nil, errors.New(errors.INVALID_PARAMETER, "用户ID不能为空")
	}

	// 设置默认值和边界检查
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}
	if pageSize > 100 {
		log.Warn().Int("requested_page_size", pageSize).Msg("请求的页面大小超过限制，使用默认值")
		pageSize = 100
	}

	followers, total, err := s.followRepo.GetFollowers(ctx, userKSUID, page, pageSize)
	if err != nil {
		log.Error().Err(err).Str("user_ksuid", userKSUID).Msg("获取粉丝列表失败")
		return nil, errors.New(errors.INTERNAL_ERROR, "获取粉丝列表失败")
	}

	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))

	// 转换为UserFollowInfo切片
	users := make([]dto.UserFollowInfo, len(followers))
	for i, follower := range followers {
		users[i] = *follower
	}

	log.Debug().
		Str("user_ksuid", userKSUID).
		Int64("total", total).
		Int("page", page).
		Int("page_size", pageSize).
		Int("returned_count", len(users)).
		Msg("获取粉丝列表成功")

	return &dto.FollowListResponse{
		Users:      users,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	}, nil
}

// GetFollowing 获取关注列表
func (s *FollowService) GetFollowing(ctx context.Context, userKSUID string, page, pageSize int) (*dto.FollowListResponse, error) {
	// 验证输入参数
	if userKSUID == "" {
		log.Error().Msg("用户ID为空")
		return nil, errors.New(errors.INVALID_PARAMETER, "用户ID不能为空")
	}

	// 设置默认值和边界检查
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}
	if pageSize > 100 {
		log.Warn().Int("requested_page_size", pageSize).Msg("请求的页面大小超过限制，使用默认值")
		pageSize = 100
	}

	following, total, err := s.followRepo.GetFollowing(ctx, userKSUID, page, pageSize)
	if err != nil {
		log.Error().Err(err).Str("user_ksuid", userKSUID).Msg("获取关注列表失败")
		return nil, errors.New(errors.INTERNAL_ERROR, "获取关注列表失败")
	}

	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))

	// 转换为UserFollowInfo切片
	users := make([]dto.UserFollowInfo, len(following))
	for i, follow := range following {
		users[i] = *follow
	}

	return &dto.FollowListResponse{
		Users:      users,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	}, nil
}

// CheckFollowStatus 检查关注状态
func (s *FollowService) CheckFollowStatus(ctx context.Context, currentUserKSUID, targetUserKSUID string) (*dto.CheckFollowStatusResponse, error) {
	// 验证输入参数
	if currentUserKSUID == "" {
		log.Error().Msg("当前用户ID为空")
		return nil, errors.New(errors.INVALID_PARAMETER, "当前用户ID不能为空")
	}

	if targetUserKSUID == "" {
		log.Error().Msg("目标用户ID为空")
		return nil, errors.New(errors.INVALID_PARAMETER, "目标用户ID不能为空")
	}

	// 检查是否关注对方
	isFollowing, err := s.followRepo.IsFollowing(ctx, currentUserKSUID, targetUserKSUID)
	if err != nil {
		log.Error().Err(err).Str("current_user", currentUserKSUID).Str("target_user", targetUserKSUID).Msg("检查关注状态失败")
		return nil, errors.New(errors.INTERNAL_ERROR, "检查关注状态失败")
	}

	// 检查是否被对方关注
	isFollowedBy, err := s.followRepo.IsFollowing(ctx, targetUserKSUID, currentUserKSUID)
	if err != nil {
		log.Error().Err(err).Str("current_user", currentUserKSUID).Str("target_user", targetUserKSUID).Msg("检查被关注状态失败")
		return nil, errors.New(errors.INTERNAL_ERROR, "检查关注状态失败")
	}

	return &dto.CheckFollowStatusResponse{
		IsFollowing:    isFollowing,
		IsFollowedBy:   isFollowedBy,
		IsMutualFollow: isFollowing && isFollowedBy,
	}, nil
}

// GetFollowStats 获取关注统计信息
func (s *FollowService) GetFollowStats(ctx context.Context, userKSUID string) (*dto.FollowStatsResponse, error) {
	// 验证输入参数
	if userKSUID == "" {
		log.Error().Msg("用户ID为空")
		return nil, errors.New(errors.INVALID_PARAMETER, "用户ID不能为空")
	}

	stats, err := s.followRepo.GetFollowStats(ctx, userKSUID)
	if err != nil {
		log.Error().Err(err).Str("user_ksuid", userKSUID).Msg("获取关注统计失败")
		return nil, errors.New(errors.INTERNAL_ERROR, "获取关注统计失败")
	}

	return stats, nil
}

// BatchCheckFollowStatus 批量检查关注状态
func (s *FollowService) BatchCheckFollowStatus(ctx context.Context, currentUserKSUID string, targetUserKSUIDs []string) (*dto.BatchCheckFollowResponse, error) {
	log.Debug().
		Str("current_user", currentUserKSUID).
		Int("target_count", len(targetUserKSUIDs)).
		Msg("开始批量检查关注状态")

	// 验证输入参数
	if currentUserKSUID == "" {
		log.Error().Msg("当前用户ID为空")
		return nil, errors.New(errors.INVALID_PARAMETER, "当前用户ID不能为空")
	}

	if len(targetUserKSUIDs) == 0 {
		log.Warn().Str("current_user", currentUserKSUID).Msg("目标用户列表为空")
		return &dto.BatchCheckFollowResponse{
			FollowStatuses: []dto.UserFollowStatus{},
		}, nil
	}

	if len(targetUserKSUIDs) > 100 {
		log.Warn().Str("current_user", currentUserKSUID).Int("count", len(targetUserKSUIDs)).Msg("批量检查用户数量超过限制")
		return nil, errors.New(errors.INVALID_PARAMETER, "最多只能检查100个用户的关注状态")
	}

	// 验证目标用户ID不能为空
	for i, userKSUID := range targetUserKSUIDs {
		if userKSUID == "" {
			log.Error().Int("index", i).Msg("目标用户列表中包含空ID")
			return nil, errors.New(errors.INVALID_PARAMETER, "目标用户ID不能为空")
		}
	}

	statuses, err := s.followRepo.BatchCheckFollowStatus(ctx, currentUserKSUID, targetUserKSUIDs)
	if err != nil {
		log.Error().Err(err).Str("current_user", currentUserKSUID).Msg("批量检查关注状态失败")
		return nil, errors.New(errors.INTERNAL_ERROR, "批量检查关注状态失败")
	}

	log.Debug().
		Str("current_user", currentUserKSUID).
		Int("checked_count", len(statuses)).
		Msg("批量检查关注状态成功")

	return &dto.BatchCheckFollowResponse{
		FollowStatuses: statuses,
	}, nil
}

// CheckFollowersPrivacy 检查用户粉丝列表是否公开
func (s *FollowService) CheckFollowersPrivacy(ctx context.Context, userKSUID string) (bool, error) {
	user, err := s.userRepo.GetByUserKSUID(ctx, userKSUID)
	if err != nil {
		if err == repository.ErrUserNotFound {
			return false, errors.New(errors.DATA_NOT_FOUND, "用户不存在")
		}
		log.Error().Err(err).Str("user_ksuid", userKSUID).Msg("获取用户信息失败")
		return false, errors.New(errors.INTERNAL_ERROR, "获取用户信息失败")
	}

	return user.IsFansPublic, nil
}

// CheckFollowingPrivacy 检查用户关注列表是否公开
func (s *FollowService) CheckFollowingPrivacy(ctx context.Context, userKSUID string) (bool, error) {
	user, err := s.userRepo.GetByUserKSUID(ctx, userKSUID)
	if err != nil {
		if err == repository.ErrUserNotFound {
			return false, errors.New(errors.DATA_NOT_FOUND, "用户不存在")
		}
		log.Error().Err(err).Str("user_ksuid", userKSUID).Msg("获取用户信息失败")
		return false, errors.New(errors.INTERNAL_ERROR, "获取用户信息失败")
	}

	return user.IsFollowPublic, nil
}
