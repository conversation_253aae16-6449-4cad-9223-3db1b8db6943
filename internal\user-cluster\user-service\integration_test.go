package main

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"pxpat-backend/internal/user-cluster/user-service/dto"
	"pxpat-backend/internal/user-cluster/user-service/external/handler"
	"pxpat-backend/internal/user-cluster/user-service/external/service"
	"pxpat-backend/internal/user-cluster/user-service/model"
	"pxpat-backend/internal/user-cluster/user-service/repository/impl"
	"pxpat-backend/pkg/ksuid"
)

// FollowIntegrationTestSuite 关注功能集成测试套件
type FollowIntegrationTestSuite struct {
	suite.Suite
	db      *gorm.DB
	router  *gin.Engine
	ctx     context.Context
	user1   *model.User
	user2   *model.User
	user3   *model.User
}

func (suite *FollowIntegrationTestSuite) SetupSuite() {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 创建内存数据库
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)

	// 自动迁移表结构
	err = db.AutoMigrate(
		&model.User{},
		&model.UserFollow{},
	)
	suite.Require().NoError(err)

	suite.db = db
	suite.ctx = context.Background()

	// 创建测试用户
	suite.createTestUsers()

	// 设置路由
	suite.setupRouter()
}

func (suite *FollowIntegrationTestSuite) createTestUsers() {
	suite.user1 = &model.User{
		UserKSUID:      ksuid.GenerateKSUID(),
		Email:          "<EMAIL>",
		Username:       "user1",
		Nickname:       "用户1",
		FollowersCount: 0,
		FollowingCount: 0,
		IsFansPublic:   true,
		IsFollowPublic: true,
	}

	suite.user2 = &model.User{
		UserKSUID:      ksuid.GenerateKSUID(),
		Email:          "<EMAIL>",
		Username:       "user2",
		Nickname:       "用户2",
		FollowersCount: 0,
		FollowingCount: 0,
		IsFansPublic:   false, // 不公开粉丝列表
		IsFollowPublic: true,
	}

	suite.user3 = &model.User{
		UserKSUID:      ksuid.GenerateKSUID(),
		Email:          "<EMAIL>",
		Username:       "user3",
		Nickname:       "用户3",
		FollowersCount: 0,
		FollowingCount: 0,
		IsFansPublic:   true,
		IsFollowPublic: false, // 不公开关注列表
	}

	suite.db.Create(suite.user1)
	suite.db.Create(suite.user2)
	suite.db.Create(suite.user3)
}

func (suite *FollowIntegrationTestSuite) setupRouter() {
	// 创建仓库
	followRepo := impl.NewFollowRepository(suite.db)
	userRepo := impl.NewUserRepository(suite.db)

	// 创建服务
	followService := service.NewFollowService(followRepo, userRepo)

	// 创建处理器
	followHandler := handler.NewFollowHandler(followService)

	// 设置路由
	suite.router = gin.New()
	api := suite.router.Group("/api")
	
	// 添加认证中间件模拟
	api.Use(func(c *gin.Context) {
		// 从请求头获取用户ID用于测试
		userKSUID := c.GetHeader("X-User-KSUID")
		if userKSUID != "" {
			c.Set("user_ksuid", userKSUID)
		}
		c.Next()
	})

	api.POST("/follow/user", followHandler.FollowUser)
	api.DELETE("/follow/user", followHandler.UnfollowUser)
	api.GET("/follow/status", followHandler.CheckFollowStatus)
	api.POST("/follow/status/batch", followHandler.BatchCheckFollowStatus)
	api.GET("/follow/stats", followHandler.GetFollowStats)
	api.GET("/users/followers", followHandler.GetFollowers)
	api.GET("/users/following", followHandler.GetFollowing)
	api.GET("/users/me/followers", followHandler.GetMyFollowers)
	api.GET("/users/me/following", followHandler.GetMyFollowing)
}

func (suite *FollowIntegrationTestSuite) TearDownTest() {
	// 清理关注关系数据
	suite.db.Exec("DELETE FROM user_follows")
	
	// 重置用户关注数量
	suite.db.Model(&model.User{}).Where("1=1").Updates(map[string]interface{}{
		"followers_count": 0,
		"following_count": 0,
	})
}

func (suite *FollowIntegrationTestSuite) TestFollowWorkflow() {
	// 1. 用户1关注用户2
	suite.T().Run("User1FollowUser2", func(t *testing.T) {
		reqBody := dto.FollowUserRequest{
			FolloweeKSUID: suite.user2.UserKSUID,
		}

		jsonBody, _ := json.Marshal(reqBody)
		req := httptest.NewRequest(http.MethodPost, "/api/follow/user", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-User-KSUID", suite.user1.UserKSUID)

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, float64(0), response["code"])
	})

	// 2. 检查关注状态
	suite.T().Run("CheckFollowStatus", func(t *testing.T) {
		req := httptest.NewRequest(http.MethodGet, "/api/follow/status?followee_ksuid="+suite.user2.UserKSUID, nil)
		req.Header.Set("X-User-KSUID", suite.user1.UserKSUID)

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, float64(0), response["code"])

		data := response["data"].(map[string]interface{})
		assert.True(t, data["is_following"].(bool))
	})

	// 3. 获取关注统计
	suite.T().Run("GetFollowStats", func(t *testing.T) {
		req := httptest.NewRequest(http.MethodGet, "/api/follow/stats", nil)
		req.Header.Set("X-User-KSUID", suite.user1.UserKSUID)

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, float64(0), response["code"])

		data := response["data"].(map[string]interface{})
		assert.Equal(t, float64(1), data["following_count"])
		assert.Equal(t, float64(0), data["followers_count"])
	})

	// 4. 用户2关注用户1（形成互相关注）
	suite.T().Run("User2FollowUser1", func(t *testing.T) {
		reqBody := dto.FollowUserRequest{
			FolloweeKSUID: suite.user1.UserKSUID,
		}

		jsonBody, _ := json.Marshal(reqBody)
		req := httptest.NewRequest(http.MethodPost, "/api/follow/user", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-User-KSUID", suite.user2.UserKSUID)

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	// 5. 获取用户1的粉丝列表（应该包含用户2）
	suite.T().Run("GetUser1Followers", func(t *testing.T) {
		req := httptest.NewRequest(http.MethodGet, "/api/users/followers?user_ksuid="+suite.user1.UserKSUID+"&page=1&page_size=10", nil)
		req.Header.Set("X-User-KSUID", suite.user2.UserKSUID)

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, float64(0), response["code"])

		data := response["data"].(map[string]interface{})
		assert.Equal(t, float64(1), data["total"])
	})

	// 6. 取消关注
	suite.T().Run("UnfollowUser", func(t *testing.T) {
		reqBody := dto.UnfollowUserRequest{
			FolloweeKSUID: suite.user2.UserKSUID,
		}

		jsonBody, _ := json.Marshal(reqBody)
		req := httptest.NewRequest(http.MethodDelete, "/api/follow/user", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-User-KSUID", suite.user1.UserKSUID)

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, float64(0), response["code"])
	})

	// 7. 验证取消关注后的状态
	suite.T().Run("VerifyUnfollowStatus", func(t *testing.T) {
		req := httptest.NewRequest(http.MethodGet, "/api/follow/status?followee_ksuid="+suite.user2.UserKSUID, nil)
		req.Header.Set("X-User-KSUID", suite.user1.UserKSUID)

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		data := response["data"].(map[string]interface{})
		assert.False(t, data["is_following"].(bool))
	})
}

func (suite *FollowIntegrationTestSuite) TestPrivacyControl() {
	// 先建立一些关注关系
	suite.setupFollowRelationships()

	// 测试访问不公开的粉丝列表
	suite.T().Run("AccessPrivateFollowersList", func(t *testing.T) {
		// 用户1尝试访问用户2的粉丝列表（用户2不公开粉丝列表）
		req := httptest.NewRequest(http.MethodGet, "/api/users/followers?user_ksuid="+suite.user2.UserKSUID+"&page=1&page_size=10", nil)
		req.Header.Set("X-User-KSUID", suite.user1.UserKSUID)

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusForbidden, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response["message"], "该用户未公开粉丝列表")
	})

	// 测试访问不公开的关注列表
	suite.T().Run("AccessPrivateFollowingList", func(t *testing.T) {
		// 用户1尝试访问用户3的关注列表（用户3不公开关注列表）
		req := httptest.NewRequest(http.MethodGet, "/api/users/following?user_ksuid="+suite.user3.UserKSUID+"&page=1&page_size=10", nil)
		req.Header.Set("X-User-KSUID", suite.user1.UserKSUID)

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusForbidden, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response["message"], "该用户未公开关注列表")
	})

	// 测试用户访问自己的私有列表
	suite.T().Run("AccessOwnPrivateList", func(t *testing.T) {
		// 用户2访问自己的粉丝列表（应该成功）
		req := httptest.NewRequest(http.MethodGet, "/api/users/me/followers?page=1&page_size=10", nil)
		req.Header.Set("X-User-KSUID", suite.user2.UserKSUID)

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, float64(0), response["code"])
	})
}

func (suite *FollowIntegrationTestSuite) setupFollowRelationships() {
	// 用户1关注用户2
	follow1 := model.NewUserFollow(suite.user1.UserKSUID, suite.user2.UserKSUID)
	suite.db.Create(follow1)

	// 用户3关注用户1
	follow2 := model.NewUserFollow(suite.user3.UserKSUID, suite.user1.UserKSUID)
	suite.db.Create(follow2)
}

func (suite *FollowIntegrationTestSuite) TestBatchOperations() {
	// 测试批量检查关注状态
	suite.T().Run("BatchCheckFollowStatus", func(t *testing.T) {
		reqBody := dto.BatchCheckFollowRequest{
			UserKSUIDs: []string{suite.user2.UserKSUID, suite.user3.UserKSUID},
		}

		jsonBody, _ := json.Marshal(reqBody)
		req := httptest.NewRequest(http.MethodPost, "/api/follow/status/batch", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-User-KSUID", suite.user1.UserKSUID)

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, float64(0), response["code"])

		data := response["data"].(map[string]interface{})
		statuses := data["follow_statuses"].([]interface{})
		assert.Len(t, statuses, 2)
	})
}

func (suite *FollowIntegrationTestSuite) TestErrorCases() {
	// 测试关注自己
	suite.T().Run("FollowSelf", func(t *testing.T) {
		reqBody := dto.FollowUserRequest{
			FolloweeKSUID: suite.user1.UserKSUID,
		}

		jsonBody, _ := json.Marshal(reqBody)
		req := httptest.NewRequest(http.MethodPost, "/api/follow/user", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-User-KSUID", suite.user1.UserKSUID)

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response["message"], "不能关注自己")
	})

	// 测试重复关注
	suite.T().Run("DuplicateFollow", func(t *testing.T) {
		// 先关注一次
		reqBody := dto.FollowUserRequest{
			FolloweeKSUID: suite.user2.UserKSUID,
		}

		jsonBody, _ := json.Marshal(reqBody)
		req := httptest.NewRequest(http.MethodPost, "/api/follow/user", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-User-KSUID", suite.user1.UserKSUID)

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)

		// 再次关注（应该失败）
		w2 := httptest.NewRecorder()
		req2 := httptest.NewRequest(http.MethodPost, "/api/follow/user", bytes.NewBuffer(jsonBody))
		req2.Header.Set("Content-Type", "application/json")
		req2.Header.Set("X-User-KSUID", suite.user1.UserKSUID)

		suite.router.ServeHTTP(w2, req2)

		assert.Equal(t, http.StatusBadRequest, w2.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w2.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response["message"], "已经关注了该用户")
	})

	// 测试取消未关注的用户
	suite.T().Run("UnfollowNotFollowed", func(t *testing.T) {
		reqBody := dto.UnfollowUserRequest{
			FolloweeKSUID: suite.user3.UserKSUID,
		}

		jsonBody, _ := json.Marshal(reqBody)
		req := httptest.NewRequest(http.MethodDelete, "/api/follow/user", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-User-KSUID", suite.user1.UserKSUID)

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response["message"], "未关注该用户")
	})
}

func TestFollowIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(FollowIntegrationTestSuite))
}
