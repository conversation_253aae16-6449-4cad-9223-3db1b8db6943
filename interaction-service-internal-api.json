{"openapi": "3.0.3", "info": {"title": "Interaction Service Internal API", "description": "Internal API for service-to-service communication for interactions including favorites, likes, and play history", "version": "1.0.0", "contact": {"name": "PxPat Backend Team"}}, "servers": [{"url": "http://localhost:12009/internal/v1", "description": "Development server"}], "components": {"schemas": {"GlobalResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "Response code"}, "message": {"type": "string", "description": "Response message"}, "data": {"description": "Response data"}}}, "ErrorResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "Error code"}, "message": {"type": "string", "description": "Error message"}}}}}, "paths": {"/favorites/add": {"post": {"tags": ["Internal Favorites"], "summary": "Add to favorite (Internal)", "description": "Internal API to add content to favorite", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"user_ksuid": {"type": "string"}, "content_ksuid": {"type": "string"}, "content_type": {"type": "string", "enum": ["video", "anime", "short"]}, "folder_id": {"type": "string"}}, "required": ["user_ksuid", "content_ksuid", "content_type"]}}}}, "responses": {"200": {"description": "Added to favorite successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/favorites/remove": {"post": {"tags": ["Internal Favorites"], "summary": "Remove from favorite (Internal)", "description": "Internal API to remove content from favorite", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"user_ksuid": {"type": "string"}, "content_ksuid": {"type": "string"}, "folder_id": {"type": "string"}}, "required": ["user_ksuid", "content_ksuid"]}}}}, "responses": {"200": {"description": "Removed from favorite successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}}, "/favorites/check-status": {"post": {"tags": ["Internal Favorites"], "summary": "Check favorite status (Internal)", "description": "Internal API to check if content is favorited", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"user_ksuid": {"type": "string"}, "content_ksuid": {"type": "string"}}, "required": ["user_ksuid", "content_ksuid"]}}}}, "responses": {"200": {"description": "Favorite status", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}}, "/favorites/batch-check-status": {"post": {"tags": ["Internal Favorites"], "summary": "Batch check favorite status (Internal)", "description": "Internal API to batch check favorite status", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"user_ksuid": {"type": "string"}, "content_ksuids": {"type": "array", "items": {"type": "string"}}}, "required": ["user_ksuid", "content_ksuids"]}}}}, "responses": {"200": {"description": "Batch favorite status", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}}, "/favorites/stats/{user_ksuid}": {"get": {"tags": ["Internal Favorites"], "summary": "Get user favorite stats (Internal)", "description": "Internal API to get user's favorite statistics", "parameters": [{"name": "user_ksuid", "in": "path", "required": true, "schema": {"type": "string"}, "description": "User KSUID"}], "responses": {"200": {"description": "User favorite statistics", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}}, "/like/status": {"get": {"tags": ["Internal Likes"], "summary": "Check user like status (Internal)", "description": "Internal API to check user's like status for content", "parameters": [{"name": "user_ksuid", "in": "query", "required": true, "schema": {"type": "string"}, "description": "User KSUID"}, {"name": "content_ksuid", "in": "query", "required": true, "schema": {"type": "string"}, "description": "Content KSUID"}], "responses": {"200": {"description": "User like status", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}}, "/like/content/counts": {"get": {"tags": ["Internal Likes"], "summary": "Get content like counts (Internal)", "description": "Internal API to get content like counts", "parameters": [{"name": "content_ksuid", "in": "query", "required": true, "schema": {"type": "string"}, "description": "Content KSUID"}], "responses": {"200": {"description": "Content like counts", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}}, "/like/content/stats/batch": {"post": {"tags": ["Internal Likes"], "summary": "Batch get content like stats (Internal)", "description": "Internal API to batch get content like statistics", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"content_ksuids": {"type": "array", "items": {"type": "string"}}}, "required": ["content_ksuids"]}}}}, "responses": {"200": {"description": "Batch content like statistics", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}}, "/like/status/batch": {"post": {"tags": ["Internal Likes"], "summary": "Batch check user like status (Internal)", "description": "Internal API to batch check user like status", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"user_ksuid": {"type": "string"}, "content_ksuids": {"type": "array", "items": {"type": "string"}}}, "required": ["user_ksuid", "content_ksuids"]}}}}, "responses": {"200": {"description": "Batch user like status", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}}, "/play-history": {"post": {"tags": ["Internal Play History"], "summary": "Update play history (Internal)", "description": "Internal API to update user's play history", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"user_ksuid": {"type": "string"}, "content_ksuid": {"type": "string"}, "content_type": {"type": "string", "enum": ["video", "anime", "short"]}, "play_duration": {"type": "integer", "description": "Play duration in seconds"}}, "required": ["user_ksuid", "content_ksuid", "content_type", "play_duration"]}}}}, "responses": {"200": {"description": "Play history updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}}, "/play-history/get": {"post": {"tags": ["Internal Play History"], "summary": "Get play history (Internal)", "description": "Internal API to get user's play history for specific content", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"user_ksuid": {"type": "string"}, "content_ksuid": {"type": "string"}}, "required": ["user_ksuid", "content_ksuid"]}}}}, "responses": {"200": {"description": "Play history record", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}}, "/play-history/exists": {"get": {"tags": ["Internal Play History"], "summary": "Check play history exists (Internal)", "description": "Internal API to check if play history exists", "parameters": [{"name": "user_ksuid", "in": "query", "required": true, "schema": {"type": "string"}, "description": "User KSUID"}, {"name": "content_ksuid", "in": "query", "required": true, "schema": {"type": "string"}, "description": "Content KSUID"}], "responses": {"200": {"description": "Play history existence status", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}}, "/play-history/stats": {"get": {"tags": ["Internal Play History"], "summary": "Get user play history stats (Internal)", "description": "Internal API to get user's play history statistics", "parameters": [{"name": "user_ksuid", "in": "query", "required": true, "schema": {"type": "string"}, "description": "User KSUID"}], "responses": {"200": {"description": "User play history statistics", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}}, "/play-history/recent": {"get": {"tags": ["Internal Play History"], "summary": "Get recent play histories (Internal)", "description": "Internal API to get user's recent play histories", "parameters": [{"name": "user_ksuid", "in": "query", "required": true, "schema": {"type": "string"}, "description": "User KSUID"}, {"name": "limit", "in": "query", "schema": {"type": "integer", "default": 10}, "description": "Limit number of records"}], "responses": {"200": {"description": "Recent play history records", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}}}}