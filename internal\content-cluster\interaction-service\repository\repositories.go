package repository

import (
	"gorm.io/gorm"
)

// Repositories 仓储层集合
type Repositories struct {
	// Album相关仓储
	AlbumRepository        *AlbumRepository
	AlbumContentRepository *AlbumContentRepository

	// Favorite相关仓储
	FavoriteFolderRepository FavoriteFolderRepository
	FavoriteItemRepository   FavoriteItemRepository
	FavoriteStatsRepository  FavoriteStatsRepository

	// Like相关仓储
	LikeRepository      LikeRepository
	LikeStatsRepository LikeStatsRepository

	// PlayHistory相关仓储
	PlayHistoryRepository PlayHistoryRepository

	// 其他交互相关仓储（预留）
	// CommentRepository  CommentRepository
	// ShareRepository    ShareRepository
	// RewardRepository   RewardRepository
}

// NewRepositories 创建仓储层集合实例
func NewRepositories(
	db *gorm.DB,
	favoriteFolderRepo FavoriteFolderRepository,
	favoriteItemRepo FavoriteItemRepository,
	favoriteStatsRepo FavoriteStatsRepository,
	likeRepo LikeRepository,
	likeStatsRepo LikeStatsRepository,
	playHistoryRepo PlayHistoryRepository,
) *Repositories {
	// 创建Album相关仓储
	albumRepo := NewAlbumRepository(db)
	albumContentRepo := NewAlbumContentRepository(db)

	return &Repositories{
		// Album相关
		AlbumRepository:        albumRepo,
		AlbumContentRepository: albumContentRepo,

		// Favorite相关
		FavoriteFolderRepository: favoriteFolderRepo,
		FavoriteItemRepository:   favoriteItemRepo,
		FavoriteStatsRepository:  favoriteStatsRepo,

		// Like相关
		LikeRepository:      likeRepo,
		LikeStatsRepository: likeStatsRepo,

		// PlayHistory相关
		PlayHistoryRepository: playHistoryRepo,

		// 其他交互相关（暂时为nil，后续实现）
		// CommentRepository: nil,
		// ShareRepository:   nil,
		// RewardRepository:  nil,
	}
}

// GetFavoriteFolderRepository 获取收藏夹仓储
func (r *Repositories) GetFavoriteFolderRepository() FavoriteFolderRepository {
	return r.FavoriteFolderRepository
}

// GetFavoriteItemRepository 获取收藏项仓储
func (r *Repositories) GetFavoriteItemRepository() FavoriteItemRepository {
	return r.FavoriteItemRepository
}

// GetAlbumRepository 获取合集仓储
func (r *Repositories) GetAlbumRepository() *AlbumRepository {
	return r.AlbumRepository
}

// GetAlbumContentRepository 获取合集内容仓储
func (r *Repositories) GetAlbumContentRepository() *AlbumContentRepository {
	return r.AlbumContentRepository
}

// GetLikeRepository 获取点赞仓储
func (r *Repositories) GetLikeRepository() LikeRepository {
	return r.LikeRepository
}

// GetLikeStatsRepository 获取点赞统计仓储
func (r *Repositories) GetLikeStatsRepository() LikeStatsRepository {
	return r.LikeStatsRepository
}

// GetPlayHistoryRepository 获取播放历史仓储
func (r *Repositories) GetPlayHistoryRepository() PlayHistoryRepository {
	return r.PlayHistoryRepository
}

// GetFavoriteStatsRepository 获取收藏统计仓储
func (r *Repositories) GetFavoriteStatsRepository() FavoriteStatsRepository {
	return r.FavoriteStatsRepository
}
