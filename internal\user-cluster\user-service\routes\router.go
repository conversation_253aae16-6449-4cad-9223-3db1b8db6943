package routes

import (
	"pxpat-backend/internal/user-cluster/user-service/external/handler"
	intraHandler "pxpat-backend/internal/user-cluster/user-service/intra/handler"
	"pxpat-backend/internal/user-cluster/user-service/routes/admin"
	"pxpat-backend/internal/user-cluster/user-service/routes/alias"
	"pxpat-backend/internal/user-cluster/user-service/routes/follow"
	"pxpat-backend/internal/user-cluster/user-service/routes/level"
	"pxpat-backend/internal/user-cluster/user-service/routes/role"
	"pxpat-backend/internal/user-cluster/user-service/routes/user"
	"pxpat-backend/internal/user-cluster/user-service/types"
	"pxpat-backend/pkg/auth"
	pkgauth "pxpat-backend/pkg/middleware/auth"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

// RegisterRoutes 注册所有路由
func RegisterRoutes(
	router *gin.Engine,
	jwtManager *auth.JWTManager,
	config *types.Config,
	userHandler *handler.UserHandler,
	roleHandler *handler.RoleHandler,
	levelHandler *handler.LevelHandler,
	aliasHandler *handler.AliasHandler,
	followHandler *handler.FollowHandler,
	internalUserHandler *intraHandler.InternalUserHandler,
	internalRoleHandler *intraHandler.InternalRoleHandler,
	internalAliasHandler *intraHandler.InternalAliasHandler,
) {
	// 认证中间件
	authMiddleware := pkgauth.UserAuthMiddleware(jwtManager)
	adminAuthMiddleware := pkgauth.AdminAuthMiddleware(jwtManager)

	// API路由组
	apiGroup := router.Group("/api")

	// 用户API路由（外部和内部）
	user.RegisterUserRouter(apiGroup, userHandler, internalUserHandler, authMiddleware)
	// 别名API路由（外部和内部）
	alias.RegisterAliasRouter(apiGroup, aliasHandler, internalAliasHandler, authMiddleware)
	// 角色API路由（外部和内部）
	role.RegisterRoleRouter(apiGroup, roleHandler, internalRoleHandler, authMiddleware, config)
	// 等级API路由（外部和内部）
	level.RegisterLevelRouter(apiGroup, levelHandler, authMiddleware)
	// 关注API路由
	follow.RegisterFollowRoutes(apiGroup, followHandler, authMiddleware)
	// 管理员API路由（外部和内部）
	admin.RegisterAdminRouter(apiGroup, userHandler, authMiddleware, adminAuthMiddleware)

	// Swagger 文档路由
	router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
}
