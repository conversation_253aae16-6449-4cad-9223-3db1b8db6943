package dto

import (
	"time"
)

type SendRegisterEmailRequest struct {
	Email string `json:"email"`
	Ip    string `json:"ip"`
}

type SendRegisterMailResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// RegisterRequest 注册请求DTO
type RegisterRequest struct {
	Email      string `json:"email"`
	Password   string `json:"password"`
	Nickname   string `json:"nickname"`
	InviteCode string `json:"invite_code"`
	Domain     string `json:"domain"`
	Ip         string `json:"ip"`
	Code       string `json:"code"`
}

// RegisterResponse 注册响应DTO
type RegisterResponse struct {
	Token  string   `json:"token"`
	User   UserInfo `json:"user"`
	Region string   `json:"region"`
}

// RegisterTestResponse 测试注册响应DTO
type RegisterTestResponse struct {
	Token  string   `json:"token"`
	Region string   `json:"region"`
	User   UserInfo `json:"user"`
}

// ========= 用户模型结构体定义 =========

// UserModel 用户完整信息模型（用于内部服务间调用）
type UserModel struct {
	ID             int       `json:"id"`
	UserKSUID      string    `json:"user_ksuid"`
	Email          string    `json:"email"`
	Username       string    `json:"username"`
	Nickname       string    `json:"nickname"`
	AvatarURL      string    `json:"avatar_url"`
	FanCount       int       `json:"fan_count"`
	FollowingCount int64     `json:"following_count"`
	LikeReceived   int64     `json:"like_received"`
	IsVerified     bool      `json:"is_verified"`
	Gender         string    `json:"gender"`
	Bio            string    `json:"bio"`
	Birthday       string    `json:"birthday"`
	Region         string    `json:"region"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
	// 隐私设置字段
	HideComment    bool `json:"hide_comment"`
	SecretLike     bool `json:"secret_like"`
	SecretFavorite bool `json:"secret_favorite"`
}

// ExportUserModel 精简版用户模型（供外部访问的数据库字段）
type ExportUserModel struct {
	UserKSUID      string `json:"user_ksuid"`
	Email          string `json:"email"`
	Username       string `json:"username"`
	Nickname       string `json:"nickname"`
	AvatarURL      string `json:"avatar_url"`
	FanCount       int    `json:"fan_count"`
	FollowingCount int64  `json:"following_count"`
	LikeReceived   int64  `json:"like_received"`
	IsVerified     bool   `json:"is_verified"`
	Gender         string `json:"gender"`
	Bio            string `json:"bio"`
	Birthday       string `json:"birthday"`
	Region         string `json:"region"`
	HideComment    bool   `json:"hide_comment"`
}

// SimpleUserModel 最精简用户模型（基本信息）
type SimpleUserModel struct {
	UserKSUID   string `json:"user_ksuid"`
	Username    string `json:"username"`
	Nickname    string `json:"nickname"`
	Avatar      string `json:"avatar"`
	Bio         string `json:"bio"`
	HideComment bool   `json:"hide_comment"`
}

// ========= 向后兼容的类型别名 =========

// UserInfo 用户信息DTO（向后兼容，映射到ExportUserModel的子集）
type UserInfo struct {
	UserKSUID string `json:"user_ksuid"`
	Email     string `json:"email"`
	Username  string `json:"username"`
	Region    string `json:"region"`
}

// UserBasicInfo 用户基本信息DTO（向后兼容，使用SimpleUserModel）
type UserBasicInfo = SimpleUserModel

// UserFullInfo 用户完整信息DTO（向后兼容，使用UserModel的子集）
type UserFullInfo struct {
	UserKSUID      string `json:"user_ksuid"`
	Username       string `json:"username"`
	Nickname       string `json:"nickname"`
	Avatar         string `json:"avatar"`
	Bio            string `json:"bio"`
	HideComment    bool   `json:"hide_comment"`
	SecretLike     bool   `json:"secret_like"`
	SecretFavorite bool   `json:"secret_favorite"`
}

// UserDTO 用户信息DTO（向后兼容，使用UserModel）
type UserDTO = UserModel

// GetUserByAliasRequest 根据别名获取用户请求DTO
type GetUserByAliasRequest struct {
	AliasName string `json:"alias_name" binding:"required"`
}

// LoginRequest 登录请求DTO
type LoginRequest struct {
	Email    string `json:"email"`
	Password string `json:"password"`
	Domain   string `json:"domain"`
	Ip       string `json:"ip"`
}

// LoginResponseDTO 登录响应DTO
type LoginResponseDTO struct {
	Token  string `json:"token"`
	Region string `json:"region"`
}

// GetUserRequest 获取用户信息请求
type GetUserRequest struct {
	UserKSUID string `json:"user_ksuid"`
}

// GetUserResponse 获取用户信息响应
type GetUserResponse struct {
	User *UserModel `json:"user"`
}

// UpdateUserResponse 更新用户信息响应
type UpdateUserResponse struct {
	User *UserModel `json:"user"`
}

// ValidateTokenRequest 验证令牌请求
type ValidateTokenRequest struct {
	Token string `json:"token"`
}

// ValidateTokenResponse 验证令牌响应
type ValidateTokenResponse struct {
	UserKSUID string `json:"user_ksuid"`
	Valid     bool   `json:"valid"`
}

// DetectRegionRequest 检测区域请求
type DetectRegionRequest struct {
	Domain      string `json:"domain"`
	Ip          string `json:"ip"`
	PhoneNumber string `json:"phone_number"`
}

// DetectRegionResponse 检测区域响应
type DetectRegionResponse struct {
	Region string `json:"region"`
}

// UpdateProfileRequest 更新用户资料请求
type UpdateProfileRequest struct {
	KSUID    string
	Username string `json:"username"`
	Nickname string `json:"nickname"`
	Bio      string `json:"bio"`
	Birthday string `json:"birthday"`
	Gender   string `json:"gender"`
	Tags     []int  `json:"tags"`
}

// BatchCreateUserItem 批量创建用户的单个用户信息
type BatchCreateUserItem struct {
	Name     string `json:"name" binding:"required"`      // 姓名（真实姓名或艺名）
	UserType string `json:"user_type" binding:"required"` // 用户类型
	Bio      string `json:"bio"`                          // 个人简介
	Email    string `json:"email"`                        // 邮箱（可选，如果不提供会自动生成）
}

// SearchUsersRequest 搜索用户请求
type SearchUsersRequest struct {
	Query    string `json:"query" binding:"required"`         // 搜索关键词
	Page     int    `json:"page" binding:"min=1"`             // 页码，从1开始
	PageSize int    `json:"page_size" binding:"min=1,max=50"` // 每页数量，默认10，最大50
}

// SearchUsersResponse 搜索用户响应
type SearchUsersResponse struct {
	Users []UserSearchResult `json:"users"` // 搜索结果
	Total int                `json:"total"` // 总数
}

// GetUsersByKSUIDsRequest 批量获取用户信息请求
type GetUsersByKSUIDsRequest struct {
	UserKSUIDs []string `json:"user_ksuids" binding:"required,min=1,max=100"` // 用户KSUID列表，最多100个
}

// GetUsersByKSUIDsResponse 批量获取用户信息响应
type GetUsersByKSUIDsResponse struct {
	Users []SimpleUserModel `json:"users"` // 用户基本信息列表
	Total int               `json:"total"` // 返回的用户数量
}

// GetUserFullInfoRequest 获取用户完整信息请求
type GetUserFullInfoRequest struct {
	UserKSUID string `json:"user_ksuid" binding:"required"` // 用户KSUID
}

// GetUserFullInfoResponse 获取用户完整信息响应
type GetUserFullInfoResponse struct {
	User *UserFullInfo `json:"user"` // 用户完整信息
}

// UserSearchResult 用户搜索结果
type UserSearchResult struct {
	UserKSUID string `json:"user_ksuid"` // 用户KSUID
	Username  string `json:"username"`   // 用户名
	Nickname  string `json:"nickname"`   // 昵称
	UserType  string `json:"user_type"`  // 用户类型
	Bio       string `json:"bio"`        // 个人简介
}



// ========= 能量系统相关 DTO =========

// EnergyStatsDTO 能量统计 DTO
type EnergyStatsDTO struct {
	CurrentEnergy    float64 `json:"current_energy"`
	MaxEnergy        float64 `json:"max_energy"`
	EnergyRegenRate  float64 `json:"energy_regen_rate"`
	MinutesToNext    float64 `json:"minutes_to_next"`
	DailyActionCount int     `json:"daily_action_count"`
	DailyActionLimit int     `json:"daily_action_limit"`
	IsMaxed          bool    `json:"is_maxed"`
	HasReachedLimit  bool    `json:"has_reached_limit"`
}

// ConsumeEnergyDTO 消耗能量 DTO
type ConsumeEnergyDTO struct {
	Action   string `json:"action" binding:"required"`
	Metadata string `json:"metadata,omitempty"`
}

// ========= 奖励系统相关 DTO =========

// RewardStatsDTO 奖励统计 DTO
type RewardStatsDTO struct {
	PersonalLevel     int     `json:"personal_level"`
	LevelName         string  `json:"level_name"`
	LevelDisplayName  string  `json:"level_display_name"`
	CurrentMultiplier float64 `json:"current_multiplier"`
	MultiplierPercent int     `json:"multiplier_percent"`
	MinRewardRate     float64 `json:"min_reward_rate"`
	MaxRewardRate     float64 `json:"max_reward_rate"`
}

// AdjustRewardDTO 调整奖励 DTO
type AdjustRewardDTO struct {
	Change float64 `json:"change" binding:"required"`
	Reason string  `json:"reason"`
}

// UpgradePersonalLevelDTO 升级个人等级 DTO
type UpgradePersonalLevelDTO struct {
	NewLevel int    `json:"new_level" binding:"required"`
	Reason   string `json:"reason"`
}

// ========= 身份管理相关 DTO =========

// ClaimAccountDTO 认领账户 DTO
type ClaimAccountDTO struct {
	ContactInfo string `json:"contact_info" binding:"required"`
	Proof       string `json:"proof"`
}

// IdolStatsDTO 偶像统计 DTO
type IdolStatsDTO struct {
	UserKSUID       string                 `json:"user_ksuid"`
	Name            string                 `json:"name"`
	UserType        string                 `json:"user_type"`
	UserTypeDisplay string                 `json:"user_type_display"`
	Stats           map[string]interface{} `json:"stats"`
}

// ========= 等级管理相关 DTO =========

// UpgradeLevelDTO 升级等级 DTO
type UpgradeLevelDTO struct {
	NewLevel int    `json:"new_level" binding:"required"`
	Reason   string `json:"reason"`
}

// LevelStatsDTO 等级统计 DTO
type LevelStatsDTO struct {
	TotalUsers        int            `json:"total_users"`
	LevelDistribution map[string]int `json:"level_distribution"`
}

// ========= 内部服务批量创建用户相关 DTO =========

// InternalBatchCreateUserRequest 内部服务批量创建用户请求
type InternalBatchCreateUserRequest struct {
	Users []InternalBatchUserInfo `json:"users" binding:"required"`
}

// InternalBatchUserInfo 内部服务批量用户信息
type InternalBatchUserInfo struct {
	Email    string   `json:"email" binding:"required,email"`
	Username string   `json:"username"`
	Nickname string   `json:"nickname" binding:"required"`
	RealName string   `json:"real_name"`
	UserType string   `json:"user_type" binding:"required"`
	Bio      string   `json:"bio"`
	Region   string   `json:"region" binding:"required"`
	Roles    []string `json:"roles"` // 用户应该拥有的角色类型列表
}

// InternalBatchCreateUserResponse 内部服务批量创建用户响应
type InternalBatchCreateUserResponse struct {
	SuccessCount int                         `json:"success_count"`
	FailedCount  int                         `json:"failed_count"`
	Results      []InternalBatchCreateResult `json:"results"`
	Message      string                      `json:"message"`
}

// InternalBatchCreateResult 内部服务批量创建结果
type InternalBatchCreateResult struct {
	Email     string `json:"email"`
	UserKSUID string `json:"user_ksuid,omitempty"`
	Success   bool   `json:"success"`
	Error     string `json:"error,omitempty"`
	Action    string `json:"action"` // "created", "updated_roles", "skipped"
}

// ========= 批量检查相关 DTO =========

// BatchCheckUsersRequest 批量检查用户是否存在请求
type BatchCheckUsersRequest struct {
	Users []UserCheckInfo `json:"users" binding:"required,min=1,max=100"`
}

// UserCheckInfo 用户检查信息
type UserCheckInfo struct {
	Name     string `json:"name" binding:"required"`      // 姓名
	UserType string `json:"user_type" binding:"required"` // 用户类型
	Email    string `json:"email"`                        // 邮箱（可选）
}

// BatchCheckUsersResponse 批量检查用户响应
type BatchCheckUsersResponse struct {
	Results []UserCheckResult `json:"results"`
}

// UserCheckResult 用户检查结果
type UserCheckResult struct {
	Name      string `json:"name"`
	UserType  string `json:"user_type"`
	Email     string `json:"email,omitempty"`
	Exists    bool   `json:"exists"`
	UserKSUID string `json:"user_ksuid,omitempty"` // 如果存在，返回用户KSUID
}

// BatchCheckAliasRequest 批量检查别名是否存在请求
type BatchCheckAliasRequest struct {
	AliasNames []string `json:"alias_names" binding:"required,min=1,max=100"`
}

// BatchCheckAliasResponse 批量检查别名响应
type BatchCheckAliasResponse struct {
	Results []AliasCheckResult `json:"results"`
}

// AliasCheckResult 别名检查结果
type AliasCheckResult struct {
	AliasName string `json:"alias_name"`
	Exists    bool   `json:"exists"`
	UserKSUID string `json:"user_ksuid,omitempty"` // 如果存在，返回用户KSUID
}

// BatchCheckRolesRequest 批量检查角色是否存在请求
type BatchCheckRolesRequest struct {
	Roles []RoleCheckInfo `json:"roles" binding:"required,min=1,max=100"`
}

// RoleCheckInfo 角色检查信息
type RoleCheckInfo struct {
	UserKSUID string `json:"user_ksuid" binding:"required"`
	RoleType  string `json:"role_type" binding:"required"`
	Region    string `json:"region"`
}

// BatchCheckRolesResponse 批量检查角色响应
type BatchCheckRolesResponse struct {
	Results []RoleCheckResult `json:"results"`
}

// RoleCheckResult 角色检查结果
type RoleCheckResult struct {
	UserKSUID string `json:"user_ksuid"`
	RoleType  string `json:"role_type"`
	Region    string `json:"region,omitempty"`
	Exists    bool   `json:"exists"`
	RoleID    int64  `json:"role_id,omitempty"` // 如果存在，返回角色ID
}

// ========= 简单批量创建用户相关 DTO =========

// SimpleBatchCreateUsersRequest 简单批量创建用户请求
type SimpleBatchCreateUsersRequest struct {
	Count int `json:"count" binding:"required,min=1,max=100"` // 要创建的用户数量，最多100个
}

// SimpleBatchCreateUsersResponse 简单批量创建用户响应
type SimpleBatchCreateUsersResponse struct {
	UserKSUIDs []string `json:"user_ksuids"` // 创建的用户KSUID列表
	Total      int      `json:"total"`       // 成功创建的用户总数
}

// ========= 批量创建相关 DTO =========

// BatchCreateRolesRequest 批量创建角色请求
type BatchCreateRolesRequest struct {
	Roles []RoleCreateInfo `json:"roles" binding:"required,min=1,max=100"`
}

// RoleCreateInfo 角色创建信息
type RoleCreateInfo struct {
	UserKSUID string `json:"user_ksuid" binding:"required"`
	RoleType  string `json:"role_type" binding:"required"`
	Region    string `json:"region" binding:"required"`
}

// BatchCreateRolesResponse 批量创建角色响应
type BatchCreateRolesResponse struct {
	Results      []RoleCreateResult `json:"results"`
	SuccessCount int                `json:"success_count"`
	FailedCount  int                `json:"failed_count"`
}

// RoleCreateResult 角色创建结果
type RoleCreateResult struct {
	UserKSUID string `json:"user_ksuid"`
	RoleType  string `json:"role_type"`
	Region    string `json:"region"`
	Success   bool   `json:"success"`
	RoleID    int64  `json:"role_id,omitempty"` // 如果创建成功，返回角色ID
	Error     string `json:"error,omitempty"`   // 如果失败，返回错误信息
}

// BatchCreateAliasRequest 批量创建别名请求
type BatchCreateAliasRequest struct {
	Aliases []AliasCreateInfo `json:"aliases" binding:"required,min=1,max=100"`
}

// AliasCreateInfo 别名创建信息
type AliasCreateInfo struct {
	UserKSUID string `json:"user_ksuid" binding:"required"`
	AliasName string `json:"alias_name" binding:"required"`
}

// BatchCreateAliasResponse 批量创建别名响应
type BatchCreateAliasResponse struct {
	Results      []AliasCreateResult `json:"results"`
	SuccessCount int                 `json:"success_count"`
	FailedCount  int                 `json:"failed_count"`
}

// AliasCreateResult 别名创建结果
type AliasCreateResult struct {
	UserKSUID string `json:"user_ksuid"`
	AliasName string `json:"alias_name"`
	Success   bool   `json:"success"`
	Error     string `json:"error,omitempty"` // 如果失败，返回错误信息
}
