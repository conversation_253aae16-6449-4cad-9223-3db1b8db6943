package main

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"pxpat-backend/internal/user-cluster/user-service/model"
	"pxpat-backend/internal/user-cluster/user-service/repository/impl"
	"pxpat-backend/pkg/ksuid"
)

// FollowPerformanceTestSuite 关注功能性能测试套件
type FollowPerformanceTestSuite struct {
	suite.Suite
	db       *gorm.DB
	repo     *impl.FollowRepositoryImpl
	ctx      context.Context
	testUsers []*model.User
}

func (suite *FollowPerformanceTestSuite) SetupSuite() {
	// 创建内存数据库
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)

	// 自动迁移表结构
	err = db.AutoMigrate(
		&model.User{},
		&model.UserFollow{},
	)
	suite.Require().NoError(err)

	suite.db = db
	suite.repo = impl.NewFollowRepository(db).(*impl.FollowRepositoryImpl)
	suite.ctx = context.Background()

	// 创建测试数据
	suite.createTestData()
}

func (suite *FollowPerformanceTestSuite) createTestData() {
	fmt.Println("创建性能测试数据...")
	
	// 创建1000个测试用户
	users := make([]*model.User, 1000)
	for i := 0; i < 1000; i++ {
		users[i] = &model.User{
			UserKSUID:      ksuid.GenerateKSUID(),
			Email:          fmt.Sprintf("<EMAIL>", i),
			Username:       fmt.Sprintf("user%d", i),
			Nickname:       fmt.Sprintf("用户%d", i),
			FollowersCount: 0,
			FollowingCount: 0,
			IsFansPublic:   true,
			IsFollowPublic: true,
		}
	}

	// 批量插入用户
	err := suite.db.CreateInBatches(users, 100).Error
	suite.Require().NoError(err)
	
	suite.testUsers = users

	// 创建关注关系（每个用户关注前面的一些用户）
	var follows []*model.UserFollow
	for i := 1; i < 1000; i++ {
		// 每个用户关注前面的10个用户（如果存在）
		followCount := 10
		if i < 10 {
			followCount = i
		}
		
		for j := 0; j < followCount; j++ {
			follow := model.NewUserFollow(users[i].UserKSUID, users[j].UserKSUID)
			follows = append(follows, follow)
		}
	}

	// 批量插入关注关系
	err = suite.db.CreateInBatches(follows, 100).Error
	suite.Require().NoError(err)

	fmt.Printf("创建了%d个用户和%d个关注关系\n", len(users), len(follows))
}

func (suite *FollowPerformanceTestSuite) TestCreateFollowPerformance() {
	// 测试创建关注关系的性能
	suite.T().Run("CreateFollowBenchmark", func(t *testing.T) {
		followerKSUID := suite.testUsers[0].UserKSUID
		followeeKSUID := suite.testUsers[500].UserKSUID

		start := time.Now()
		
		// 执行100次创建关注操作
		for i := 0; i < 100; i++ {
			tempFolloweeKSUID := suite.testUsers[500+i].UserKSUID
			follow := model.NewUserFollow(followerKSUID, tempFolloweeKSUID)
			err := suite.repo.CreateFollow(suite.ctx, follow)
			assert.NoError(t, err)
		}
		
		duration := time.Since(start)
		avgTime := duration / 100
		
		fmt.Printf("创建关注关系平均耗时: %v\n", avgTime)
		
		// 期望每次操作在10ms以内
		assert.Less(t, avgTime, 10*time.Millisecond, "创建关注关系耗时过长")
	})
}

func (suite *FollowPerformanceTestSuite) TestIsFollowingPerformance() {
	// 测试检查关注状态的性能
	suite.T().Run("IsFollowingBenchmark", func(t *testing.T) {
		followerKSUID := suite.testUsers[100].UserKSUID
		
		start := time.Now()
		
		// 执行1000次检查关注状态操作
		for i := 0; i < 1000; i++ {
			followeeKSUID := suite.testUsers[i].UserKSUID
			_, err := suite.repo.IsFollowing(suite.ctx, followerKSUID, followeeKSUID)
			assert.NoError(t, err)
		}
		
		duration := time.Since(start)
		avgTime := duration / 1000
		
		fmt.Printf("检查关注状态平均耗时: %v\n", avgTime)
		
		// 期望每次操作在1ms以内
		assert.Less(t, avgTime, 1*time.Millisecond, "检查关注状态耗时过长")
	})
}

func (suite *FollowPerformanceTestSuite) TestGetFollowersPerformance() {
	// 测试获取粉丝列表的性能
	suite.T().Run("GetFollowersBenchmark", func(t *testing.T) {
		// 选择一个有很多粉丝的用户（用户0被很多人关注）
		userKSUID := suite.testUsers[0].UserKSUID
		
		start := time.Now()
		
		// 执行100次获取粉丝列表操作
		for i := 0; i < 100; i++ {
			_, _, err := suite.repo.GetFollowers(suite.ctx, userKSUID, 1, 20)
			assert.NoError(t, err)
		}
		
		duration := time.Since(start)
		avgTime := duration / 100
		
		fmt.Printf("获取粉丝列表平均耗时: %v\n", avgTime)
		
		// 期望每次操作在50ms以内
		assert.Less(t, avgTime, 50*time.Millisecond, "获取粉丝列表耗时过长")
	})
}

func (suite *FollowPerformanceTestSuite) TestGetFollowingPerformance() {
	// 测试获取关注列表的性能
	suite.T().Run("GetFollowingBenchmark", func(t *testing.T) {
		// 选择一个关注了很多人的用户
		userKSUID := suite.testUsers[999].UserKSUID
		
		start := time.Now()
		
		// 执行100次获取关注列表操作
		for i := 0; i < 100; i++ {
			_, _, err := suite.repo.GetFollowing(suite.ctx, userKSUID, 1, 20)
			assert.NoError(t, err)
		}
		
		duration := time.Since(start)
		avgTime := duration / 100
		
		fmt.Printf("获取关注列表平均耗时: %v\n", avgTime)
		
		// 期望每次操作在50ms以内
		assert.Less(t, avgTime, 50*time.Millisecond, "获取关注列表耗时过长")
	})
}

func (suite *FollowPerformanceTestSuite) TestBatchCheckFollowStatusPerformance() {
	// 测试批量检查关注状态的性能
	suite.T().Run("BatchCheckFollowStatusBenchmark", func(t *testing.T) {
		followerKSUID := suite.testUsers[100].UserKSUID
		
		// 准备100个用户KSUID
		var targetKSUIDs []string
		for i := 0; i < 100; i++ {
			targetKSUIDs = append(targetKSUIDs, suite.testUsers[i].UserKSUID)
		}
		
		start := time.Now()
		
		// 执行10次批量检查操作
		for i := 0; i < 10; i++ {
			_, err := suite.repo.BatchCheckFollowStatus(suite.ctx, followerKSUID, targetKSUIDs)
			assert.NoError(t, err)
		}
		
		duration := time.Since(start)
		avgTime := duration / 10
		
		fmt.Printf("批量检查关注状态平均耗时: %v\n", avgTime)
		
		// 期望每次操作在100ms以内
		assert.Less(t, avgTime, 100*time.Millisecond, "批量检查关注状态耗时过长")
	})
}

func (suite *FollowPerformanceTestSuite) TestGetFollowCountsPerformance() {
	// 测试获取关注数量的性能
	suite.T().Run("GetFollowCountsBenchmark", func(t *testing.T) {
		start := time.Now()
		
		// 执行1000次获取关注数量操作
		for i := 0; i < 1000; i++ {
			userKSUID := suite.testUsers[i].UserKSUID
			
			_, err := suite.repo.GetFollowersCount(suite.ctx, userKSUID)
			assert.NoError(t, err)
			
			_, err = suite.repo.GetFollowingCount(suite.ctx, userKSUID)
			assert.NoError(t, err)
		}
		
		duration := time.Since(start)
		avgTime := duration / 2000 // 2000次操作（每个用户2次）
		
		fmt.Printf("获取关注数量平均耗时: %v\n", avgTime)
		
		// 期望每次操作在1ms以内
		assert.Less(t, avgTime, 1*time.Millisecond, "获取关注数量耗时过长")
	})
}

func (suite *FollowPerformanceTestSuite) TestConcurrentOperations() {
	// 测试并发操作的性能
	suite.T().Run("ConcurrentOperationsBenchmark", func(t *testing.T) {
		const goroutines = 10
		const operationsPerGoroutine = 100
		
		start := time.Now()
		
		// 创建通道来等待所有goroutine完成
		done := make(chan bool, goroutines)
		
		// 启动多个goroutine并发执行操作
		for g := 0; g < goroutines; g++ {
			go func(goroutineID int) {
				defer func() { done <- true }()
				
				for i := 0; i < operationsPerGoroutine; i++ {
					userIndex := (goroutineID*operationsPerGoroutine + i) % len(suite.testUsers)
					userKSUID := suite.testUsers[userIndex].UserKSUID
					
					// 执行各种操作
					_, _ = suite.repo.GetFollowersCount(suite.ctx, userKSUID)
					_, _ = suite.repo.GetFollowingCount(suite.ctx, userKSUID)
					
					if userIndex < len(suite.testUsers)-1 {
						_, _ = suite.repo.IsFollowing(suite.ctx, userKSUID, suite.testUsers[userIndex+1].UserKSUID)
					}
				}
			}(g)
		}
		
		// 等待所有goroutine完成
		for i := 0; i < goroutines; i++ {
			<-done
		}
		
		duration := time.Since(start)
		totalOperations := goroutines * operationsPerGoroutine * 3 // 每个循环3个操作
		avgTime := duration / time.Duration(totalOperations)
		
		fmt.Printf("并发操作平均耗时: %v\n", avgTime)
		fmt.Printf("总操作数: %d, 总耗时: %v\n", totalOperations, duration)
		
		// 期望并发操作不会显著降低性能
		assert.Less(t, avgTime, 5*time.Millisecond, "并发操作耗时过长")
	})
}

func TestFollowPerformanceTestSuite(t *testing.T) {
	if testing.Short() {
		t.Skip("跳过性能测试（使用 -short 标志）")
	}
	suite.Run(t, new(FollowPerformanceTestSuite))
}
