package favorite

import (
	"github.com/gin-gonic/gin"
	"pxpat-backend/internal/content-cluster/interaction-service/intra/handler"
)

// RegisterFavoriteInternalRoutes 注册收藏功能的内部API路由
func RegisterFavoriteInternalRoutes(
	r *gin.RouterGroup,
	internalFavoriteHandler *handler.InternalFavoriteHandler,
) {
	favoriteGroup := r.Group("/favorites")
	{
		// 内部收藏操作路由
		favoriteGroup.POST("/add", internalFavoriteHandler.AddToFavoriteInternal)                                   // 内部添加收藏
		favoriteGroup.POST("/remove", internalFavoriteHandler.RemoveFromFavoriteInternal)                           // 内部移除收藏
		favoriteGroup.POST("/check-status", internalFavoriteHandler.CheckFavoriteStatusInternal)                    // 内部检查收藏状态
		favoriteGroup.POST("/batch-check-status", internalFavoriteHandler.BatchCheckFavoriteStatusInternal)         // 内部批量检查收藏状态
		favoriteGroup.GET("/stats/:user_ksuid", internalFavoriteHandler.GetUserFavoriteStatsInternal)               // 内部获取用户收藏统计
		favoriteGroup.GET("/content/:content_ksuid/count", internalFavoriteHandler.GetContentFavoriteCountInternal) // 内部获取内容收藏数
	}
}
