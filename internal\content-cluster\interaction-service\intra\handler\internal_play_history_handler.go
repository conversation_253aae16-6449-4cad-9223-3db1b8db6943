package handler

import (
	"net/http"
	"pxpat-backend/pkg/errors"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"pxpat-backend/internal/content-cluster/interaction-service/dto"
	"pxpat-backend/internal/content-cluster/interaction-service/intra/service"
	globalTypes "pxpat-backend/pkg/types"
)

type InternalPlayHistoryHandler struct {
	internalPlayHistoryService *service.InternalPlayHistoryService
}

func NewInternalPlayHistoryHandler(internalPlayHistoryService *service.InternalPlayHistoryService) *InternalPlayHistoryHandler {
	return &InternalPlayHistoryHandler{
		internalPlayHistoryService: internalPlayHistoryService,
	}
}

// UpdatePlayHistory 内部更新播放历史记录
// @Summary 内部更新播放历史记录
// @Description 内部服务调用，更新用户的播放历史记录
// @Tags 内部播放历史
// @Accept json
// @Produce json
// @Param request body dto.InternalUpdatePlayHistoryRequest true "内部更新播放历史记录请求"
// @Success 200 {object} globalTypes.GlobalResponse{data=dto.InternalUpdatePlayHistoryResponse} "更新成功"
// @Failure 400 {object} globalTypes.GlobalResponse "请求参数错误"
// @Failure 500 {object} globalTypes.GlobalResponse "服务器内部错误"
// @Router /internal/v1/play-history [post]
func (h *InternalPlayHistoryHandler) UpdatePlayHistory(c *gin.Context) {
	// 解析请求参数
	var req dto.InternalUpdatePlayHistoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().Err(err).Msg("内部更新播放历史记录请求参数无效")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	// 调用服务层
	result, gErr := h.internalPlayHistoryService.UpdatePlayHistory(c.Request.Context(), &req)
	if gErr != nil {
		log.Error().
			Err(gErr).
			Str("user_ksuid", req.UserKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Str("content_type", req.ContentType).
			Int64("play_duration", req.PlayDuration).
			Msg("内部更新播放历史记录失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	log.Info().
		Str("user_ksuid", req.UserKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Str("content_type", req.ContentType).
		Int64("play_duration", req.PlayDuration).
		Str("play_history_item_id", result.PlayHistoryItemID).
		Bool("is_new", result.IsNew).
		Msg("内部更新播放历史记录成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.HAVE_DATA_SUCCESS,
		Data: result,
	})
}

// GetPlayHistory 内部获取播放历史记录
// @Summary 内部获取播放历史记录
// @Description 内部服务调用，根据用户和内容获取播放历史记录
// @Tags 内部播放历史
// @Accept json
// @Produce json
// @Param request body dto.InternalGetPlayHistoryRequest true "内部获取播放历史记录请求"
// @Success 200 {object} globalTypes.GlobalResponse{data=dto.InternalGetPlayHistoryResponse} "获取成功"
// @Failure 400 {object} globalTypes.GlobalResponse "请求参数错误"
// @Failure 500 {object} globalTypes.GlobalResponse "服务器内部错误"
// @Router /internal/v1/play-history/get [post]
func (h *InternalPlayHistoryHandler) GetPlayHistory(c *gin.Context) {
	// 解析请求参数
	var req dto.InternalGetPlayHistoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().Err(err).Msg("内部获取播放历史记录请求参数无效")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	// 调用服务层
	result, gErr := h.internalPlayHistoryService.GetPlayHistory(c.Request.Context(), &req)
	if gErr != nil {
		log.Error().
			Err(gErr).
			Str("user_ksuid", req.UserKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Msg("内部获取播放历史记录失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	log.Debug().
		Str("user_ksuid", req.UserKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Bool("found", result.Found).
		Msg("内部获取播放历史记录成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.HAVE_DATA_SUCCESS,
		Data: result,
	})
}

// CheckPlayHistoryExists 检查播放历史是否存在
// @Summary 检查播放历史是否存在
// @Description 内部服务调用，检查用户是否有指定内容的播放历史
// @Tags 内部播放历史
// @Accept json
// @Produce json
// @Param user_ksuid query string true "用户KSUID"
// @Param content_ksuid query string true "内容KSUID"
// @Success 200 {object} globalTypes.GlobalResponse{data=map[string]bool} "检查成功"
// @Failure 400 {object} globalTypes.GlobalResponse "请求参数错误"
// @Failure 500 {object} globalTypes.GlobalResponse "服务器内部错误"
// @Router /internal/v1/play-history/exists [get]
func (h *InternalPlayHistoryHandler) CheckPlayHistoryExists(c *gin.Context) {
	// 获取请求参数
	userKSUID := c.Query("user_ksuid")
	contentKSUID := c.Query("content_ksuid")

	if userKSUID == "" || contentKSUID == "" {
		log.Error().
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", contentKSUID).
			Msg("用户KSUID或内容KSUID不能为空")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	// 调用服务层
	exists, gErr := h.internalPlayHistoryService.CheckPlayHistoryExists(c.Request.Context(), userKSUID, contentKSUID)
	if gErr != nil {
		log.Error().
			Err(gErr).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", contentKSUID).
			Msg("检查播放历史是否存在失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	result := map[string]bool{
		"exists": exists,
	}

	log.Debug().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", contentKSUID).
		Bool("exists", exists).
		Msg("检查播放历史是否存在成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.HAVE_DATA_SUCCESS,
		Data: result,
	})
}

// GetUserPlayHistoryStats 获取用户播放历史统计信息
// @Summary 获取用户播放历史统计信息
// @Description 内部服务调用，获取用户播放历史统计信息
// @Tags 内部播放历史
// @Accept json
// @Produce json
// @Param user_ksuid query string true "用户KSUID"
// @Success 200 {object} globalTypes.GlobalResponse{data=map[string]int64} "获取成功"
// @Failure 400 {object} globalTypes.GlobalResponse "请求参数错误"
// @Failure 500 {object} globalTypes.GlobalResponse "服务器内部错误"
// @Router /internal/v1/play-history/stats [get]
func (h *InternalPlayHistoryHandler) GetUserPlayHistoryStats(c *gin.Context) {
	// 获取请求参数
	userKSUID := c.Query("user_ksuid")

	if userKSUID == "" {
		log.Error().Str("user_ksuid", userKSUID).Msg("用户KSUID不能为空")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	// 调用服务层
	stats, gErr := h.internalPlayHistoryService.GetUserPlayHistoryStats(c.Request.Context(), userKSUID)
	if gErr != nil {
		log.Error().
			Err(gErr).
			Str("user_ksuid", userKSUID).
			Msg("获取用户播放历史统计信息失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	log.Debug().
		Str("user_ksuid", userKSUID).
		Interface("stats", stats).
		Msg("获取用户播放历史统计信息成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.HAVE_DATA_SUCCESS,
		Data: stats,
	})
}

// GetRecentPlayHistories 获取用户最近播放的内容
// @Summary 获取用户最近播放的内容
// @Description 内部服务调用，获取用户最近播放的内容
// @Tags 内部播放历史
// @Accept json
// @Produce json
// @Param user_ksuid query string true "用户KSUID"
// @Param limit query int false "限制数量，默认10" default(10)
// @Success 200 {object} globalTypes.GlobalResponse{data=[]dto.PlayHistoryItemDTO} "获取成功"
// @Failure 400 {object} globalTypes.GlobalResponse "请求参数错误"
// @Failure 500 {object} globalTypes.GlobalResponse "服务器内部错误"
// @Router /internal/v1/play-history/recent [get]
func (h *InternalPlayHistoryHandler) GetRecentPlayHistories(c *gin.Context) {
	// 获取请求参数
	userKSUID := c.Query("user_ksuid")
	limit := 10 // 默认值

	if userKSUID == "" {
		log.Error().Str("user_ksuid", userKSUID).Msg("用户KSUID不能为空")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	// 解析limit参数
	if limitStr := c.Query("limit"); limitStr != "" {
		if parsedLimit, err := strconv.Atoi(limitStr); err == nil && parsedLimit > 0 {
			limit = parsedLimit
		}
	}

	// 调用服务层
	playHistories, gErr := h.internalPlayHistoryService.GetRecentPlayHistories(c.Request.Context(), userKSUID, limit)
	if gErr != nil {
		log.Error().
			Err(gErr).
			Str("user_ksuid", userKSUID).
			Int("limit", limit).
			Msg("获取用户最近播放的内容失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	log.Debug().
		Str("user_ksuid", userKSUID).
		Int("limit", limit).
		Int("count", len(playHistories)).
		Msg("获取用户最近播放的内容成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.HAVE_DATA_SUCCESS,
		Data: playHistories,
	})
}
