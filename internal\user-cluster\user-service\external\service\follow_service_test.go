package service

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
	"pxpat-backend/internal/user-cluster/user-service/dto"
	"pxpat-backend/internal/user-cluster/user-service/model"
	"pxpat-backend/pkg/errors"
)

// MockFollowRepository 模拟关注仓库
type MockFollowRepository struct {
	mock.Mock
}

func (m *MockFollowRepository) CreateFollow(ctx context.Context, followerKSUID, followeeKSUID string) error {
	args := m.Called(ctx, followerKSUID, followeeKSUID)
	return args.Error(0)
}

func (m *MockFollowRepository) DeleteFollow(ctx context.Context, followerKSUID, followeeKSUID string) error {
	args := m.Called(ctx, followerKSUID, followeeKSUID)
	return args.Error(0)
}

func (m *MockFollowRepository) IsFollowing(ctx context.Context, followerKSUID, followeeKSUID string) (bool, error) {
	args := m.Called(ctx, followerKSUID, followeeKSUID)
	return args.Bool(0), args.Error(1)
}

func (m *MockFollowRepository) GetFollowers(ctx context.Context, userKSUID string, page, pageSize int) ([]*model.UserFollow, int64, error) {
	args := m.Called(ctx, userKSUID, page, pageSize)
	return args.Get(0).([]*model.UserFollow), args.Get(1).(int64), args.Error(2)
}

func (m *MockFollowRepository) GetFollowing(ctx context.Context, userKSUID string, page, pageSize int) ([]*model.UserFollow, int64, error) {
	args := m.Called(ctx, userKSUID, page, pageSize)
	return args.Get(0).([]*model.UserFollow), args.Get(1).(int64), args.Error(2)
}

func (m *MockFollowRepository) GetFollowersCount(ctx context.Context, userKSUID string) (int64, error) {
	args := m.Called(ctx, userKSUID)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockFollowRepository) GetFollowingCount(ctx context.Context, userKSUID string) (int64, error) {
	args := m.Called(ctx, userKSUID)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockFollowRepository) UpdateUserFollowCounts(ctx context.Context, userKSUID string) error {
	args := m.Called(ctx, userKSUID)
	return args.Error(0)
}

func (m *MockFollowRepository) BatchCheckFollowStatus(ctx context.Context, followerKSUID string, followeeKSUIDs []string) (map[string]bool, error) {
	args := m.Called(ctx, followerKSUID, followeeKSUIDs)
	return args.Get(0).(map[string]bool), args.Error(1)
}

func (m *MockFollowRepository) GetMutualFollows(ctx context.Context, userKSUID string, page, pageSize int) ([]*model.UserFollow, int64, error) {
	args := m.Called(ctx, userKSUID, page, pageSize)
	return args.Get(0).([]*model.UserFollow), args.Get(1).(int64), args.Error(2)
}

// MockUserRepository 模拟用户仓库
type MockUserRepository struct {
	mock.Mock
}

func (m *MockUserRepository) GetUserByKSUID(ctx context.Context, userKSUID string) (*model.User, error) {
	args := m.Called(ctx, userKSUID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.User), args.Error(1)
}

// FollowServiceTestSuite 关注服务测试套件
type FollowServiceTestSuite struct {
	suite.Suite
	service    *FollowService
	mockFollow *MockFollowRepository
	mockUser   *MockUserRepository
	ctx        context.Context
}

func (suite *FollowServiceTestSuite) SetupTest() {
	suite.mockFollow = new(MockFollowRepository)
	suite.mockUser = new(MockUserRepository)
	suite.service = NewFollowService(suite.mockFollow, suite.mockUser)
	suite.ctx = context.Background()
}

func (suite *FollowServiceTestSuite) TestFollowUser_Success() {
	followerKSUID := "test_user_001"
	followeeKSUID := "test_user_002"

	req := &dto.FollowUserRequest{
		FolloweeKSUID: followeeKSUID,
	}

	// 设置mock期望
	suite.mockFollow.On("IsFollowing", suite.ctx, followerKSUID, followeeKSUID).Return(false, nil)
	suite.mockFollow.On("CreateFollow", suite.ctx, followerKSUID, followeeKSUID).Return(nil)
	suite.mockFollow.On("UpdateUserFollowCounts", suite.ctx, followerKSUID).Return(nil)
	suite.mockFollow.On("UpdateUserFollowCounts", suite.ctx, followeeKSUID).Return(nil)

	// 执行测试
	resp, err := suite.service.FollowUser(suite.ctx, followerKSUID, req)

	// 验证结果
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), resp)
	assert.True(suite.T(), resp.Success)
	assert.Equal(suite.T(), "关注成功", resp.Message)

	// 验证mock调用
	suite.mockFollow.AssertExpectations(suite.T())
}

func (suite *FollowServiceTestSuite) TestFollowUser_SelfFollow() {
	userKSUID := "test_user_001"

	req := &dto.FollowUserRequest{
		FolloweeKSUID: userKSUID,
	}

	// 执行测试
	resp, err := suite.service.FollowUser(suite.ctx, userKSUID, req)

	// 验证结果
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), resp)
	assert.Contains(suite.T(), err.Error(), "不能关注自己")
}

func (suite *FollowServiceTestSuite) TestFollowUser_AlreadyFollowing() {
	followerKSUID := "test_user_001"
	followeeKSUID := "test_user_002"

	req := &dto.FollowUserRequest{
		FolloweeKSUID: followeeKSUID,
	}

	// 设置mock期望
	suite.mockFollow.On("IsFollowing", suite.ctx, followerKSUID, followeeKSUID).Return(true, nil)

	// 执行测试
	resp, err := suite.service.FollowUser(suite.ctx, followerKSUID, req)

	// 验证结果
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), resp)
	assert.Contains(suite.T(), err.Error(), "已经关注了该用户")

	// 验证mock调用
	suite.mockFollow.AssertExpectations(suite.T())
}

func (suite *FollowServiceTestSuite) TestUnfollowUser_Success() {
	followerKSUID := "test_user_001"
	followeeKSUID := "test_user_002"

	req := &dto.UnfollowUserRequest{
		FolloweeKSUID: followeeKSUID,
	}

	// 设置mock期望
	suite.mockFollow.On("IsFollowing", suite.ctx, followerKSUID, followeeKSUID).Return(true, nil)
	suite.mockFollow.On("DeleteFollow", suite.ctx, followerKSUID, followeeKSUID).Return(nil)
	suite.mockFollow.On("UpdateUserFollowCounts", suite.ctx, followerKSUID).Return(nil)
	suite.mockFollow.On("UpdateUserFollowCounts", suite.ctx, followeeKSUID).Return(nil)

	// 执行测试
	resp, err := suite.service.UnfollowUser(suite.ctx, followerKSUID, req)

	// 验证结果
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), resp)
	assert.True(suite.T(), resp.Success)
	assert.Equal(suite.T(), "取消关注成功", resp.Message)

	// 验证mock调用
	suite.mockFollow.AssertExpectations(suite.T())
}

func (suite *FollowServiceTestSuite) TestUnfollowUser_NotFollowing() {
	followerKSUID := "test_user_001"
	followeeKSUID := "test_user_002"

	req := &dto.UnfollowUserRequest{
		FolloweeKSUID: followeeKSUID,
	}

	// 设置mock期望
	suite.mockFollow.On("IsFollowing", suite.ctx, followerKSUID, followeeKSUID).Return(false, nil)

	// 执行测试
	resp, err := suite.service.UnfollowUser(suite.ctx, followerKSUID, req)

	// 验证结果
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), resp)
	assert.Contains(suite.T(), err.Error(), "未关注该用户")

	// 验证mock调用
	suite.mockFollow.AssertExpectations(suite.T())
}

func (suite *FollowServiceTestSuite) TestCheckFollowStatus_Success() {
	followerKSUID := "test_user_001"
	followeeKSUID := "test_user_002"

	req := &dto.CheckFollowStatusRequest{
		FolloweeKSUID: followeeKSUID,
	}

	// 设置mock期望
	suite.mockFollow.On("IsFollowing", suite.ctx, followerKSUID, followeeKSUID).Return(true, nil)

	// 执行测试
	resp, err := suite.service.CheckFollowStatus(suite.ctx, followerKSUID, req)

	// 验证结果
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), resp)
	assert.True(suite.T(), resp.IsFollowing)

	// 验证mock调用
	suite.mockFollow.AssertExpectations(suite.T())
}

func (suite *FollowServiceTestSuite) TestBatchCheckFollowStatus_Success() {
	followerKSUID := "test_user_001"
	followeeKSUIDs := []string{"test_user_002", "test_user_003"}

	req := &dto.BatchCheckFollowRequest{
		FolloweeKSUIDs: followeeKSUIDs,
	}

	expectedStatusMap := map[string]bool{
		"test_user_002": true,
		"test_user_003": false,
	}

	// 设置mock期望
	suite.mockFollow.On("BatchCheckFollowStatus", suite.ctx, followerKSUID, followeeKSUIDs).Return(expectedStatusMap, nil)

	// 执行测试
	resp, err := suite.service.BatchCheckFollowStatus(suite.ctx, followerKSUID, req)

	// 验证结果
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), resp)
	assert.Equal(suite.T(), expectedStatusMap, resp.FollowStatus)

	// 验证mock调用
	suite.mockFollow.AssertExpectations(suite.T())
}

func (suite *FollowServiceTestSuite) TestGetFollowStats_Success() {
	userKSUID := "test_user_001"

	// 设置mock期望
	suite.mockFollow.On("GetFollowersCount", suite.ctx, userKSUID).Return(int64(10), nil)
	suite.mockFollow.On("GetFollowingCount", suite.ctx, userKSUID).Return(int64(5), nil)

	// 执行测试
	resp, err := suite.service.GetFollowStats(suite.ctx, userKSUID)

	// 验证结果
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), resp)
	assert.Equal(suite.T(), int64(10), resp.FollowersCount)
	assert.Equal(suite.T(), int64(5), resp.FollowingCount)

	// 验证mock调用
	suite.mockFollow.AssertExpectations(suite.T())
}

func (suite *FollowServiceTestSuite) TestGetFollowers_Success() {
	targetUserKSUID := "test_user_001"
	currentUserKSUID := "test_user_002"

	req := &dto.GetFollowersRequest{
		UserKSUID: targetUserKSUID,
		Page:      1,
		PageSize:  10,
	}

	expectedFollows := []*model.UserFollow{
		{
			FollowerKSUID: "test_user_003",
			FolloweeKSUID: targetUserKSUID,
			Follower: &model.User{
				UserKSUID: "test_user_003",
				Nickname:  "用户3",
			},
		},
	}

	targetUser := &model.User{
		UserKSUID:     targetUserKSUID,
		IsFansPublic:  true,
		FollowersCount: 1,
	}

	// 设置mock期望
	suite.mockUser.On("GetUserByKSUID", suite.ctx, targetUserKSUID).Return(targetUser, nil)
	suite.mockFollow.On("GetFollowers", suite.ctx, targetUserKSUID, 1, 10).Return(expectedFollows, int64(1), nil)

	// 执行测试
	resp, err := suite.service.GetFollowers(suite.ctx, currentUserKSUID, req)

	// 验证结果
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), resp)
	assert.Len(suite.T(), resp.Followers, 1)
	assert.Equal(suite.T(), int64(1), resp.Total)

	// 验证mock调用
	suite.mockUser.AssertExpectations(suite.T())
	suite.mockFollow.AssertExpectations(suite.T())
}

func (suite *FollowServiceTestSuite) TestGetFollowers_PrivacyDenied() {
	targetUserKSUID := "test_user_001"
	currentUserKSUID := "test_user_002"

	req := &dto.GetFollowersRequest{
		UserKSUID: targetUserKSUID,
		Page:      1,
		PageSize:  10,
	}

	targetUser := &model.User{
		UserKSUID:    targetUserKSUID,
		IsFansPublic: false, // 不公开粉丝列表
	}

	// 设置mock期望
	suite.mockUser.On("GetUserByKSUID", suite.ctx, targetUserKSUID).Return(targetUser, nil)

	// 执行测试
	resp, err := suite.service.GetFollowers(suite.ctx, currentUserKSUID, req)

	// 验证结果
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), resp)
	assert.Contains(suite.T(), err.Error(), "该用户未公开粉丝列表")

	// 验证mock调用
	suite.mockUser.AssertExpectations(suite.T())
}

func (suite *FollowServiceTestSuite) TestGetFollowing_Success() {
	targetUserKSUID := "test_user_001"
	currentUserKSUID := "test_user_002"

	req := &dto.GetFollowingRequest{
		UserKSUID: targetUserKSUID,
		Page:      1,
		PageSize:  10,
	}

	expectedFollows := []*model.UserFollow{
		{
			FollowerKSUID: targetUserKSUID,
			FolloweeKSUID: "test_user_003",
			Followee: &model.User{
				UserKSUID: "test_user_003",
				Nickname:  "用户3",
			},
		},
	}

	targetUser := &model.User{
		UserKSUID:      targetUserKSUID,
		IsFollowPublic: true,
		FollowingCount: 1,
	}

	// 设置mock期望
	suite.mockUser.On("GetUserByKSUID", suite.ctx, targetUserKSUID).Return(targetUser, nil)
	suite.mockFollow.On("GetFollowing", suite.ctx, targetUserKSUID, 1, 10).Return(expectedFollows, int64(1), nil)

	// 执行测试
	resp, err := suite.service.GetFollowing(suite.ctx, currentUserKSUID, req)

	// 验证结果
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), resp)
	assert.Len(suite.T(), resp.Following, 1)
	assert.Equal(suite.T(), int64(1), resp.Total)

	// 验证mock调用
	suite.mockUser.AssertExpectations(suite.T())
	suite.mockFollow.AssertExpectations(suite.T())
}

func (suite *FollowServiceTestSuite) TestGetFollowing_PrivacyDenied() {
	targetUserKSUID := "test_user_001"
	currentUserKSUID := "test_user_002"

	req := &dto.GetFollowingRequest{
		UserKSUID: targetUserKSUID,
		Page:      1,
		PageSize:  10,
	}

	targetUser := &model.User{
		UserKSUID:      targetUserKSUID,
		IsFollowPublic: false, // 不公开关注列表
	}

	// 设置mock期望
	suite.mockUser.On("GetUserByKSUID", suite.ctx, targetUserKSUID).Return(targetUser, nil)

	// 执行测试
	resp, err := suite.service.GetFollowing(suite.ctx, currentUserKSUID, req)

	// 验证结果
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), resp)
	assert.Contains(suite.T(), err.Error(), "该用户未公开关注列表")

	// 验证mock调用
	suite.mockUser.AssertExpectations(suite.T())
}

func (suite *FollowServiceTestSuite) TestGetFollowers_SelfAccess() {
	userKSUID := "test_user_001"

	req := &dto.GetFollowersRequest{
		UserKSUID: userKSUID,
		Page:      1,
		PageSize:  10,
	}

	expectedFollows := []*model.UserFollow{
		{
			FollowerKSUID: "test_user_002",
			FolloweeKSUID: userKSUID,
			Follower: &model.User{
				UserKSUID: "test_user_002",
				Nickname:  "用户2",
			},
		},
	}

	targetUser := &model.User{
		UserKSUID:     userKSUID,
		IsFansPublic:  false, // 不公开，但是自己访问自己应该可以
		FollowersCount: 1,
	}

	// 设置mock期望
	suite.mockUser.On("GetUserByKSUID", suite.ctx, userKSUID).Return(targetUser, nil)
	suite.mockFollow.On("GetFollowers", suite.ctx, userKSUID, 1, 10).Return(expectedFollows, int64(1), nil)

	// 执行测试（自己访问自己的粉丝列表）
	resp, err := suite.service.GetFollowers(suite.ctx, userKSUID, req)

	// 验证结果
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), resp)
	assert.Len(suite.T(), resp.Followers, 1)

	// 验证mock调用
	suite.mockUser.AssertExpectations(suite.T())
	suite.mockFollow.AssertExpectations(suite.T())
}

func TestFollowServiceTestSuite(t *testing.T) {
	suite.Run(t, new(FollowServiceTestSuite))
}
