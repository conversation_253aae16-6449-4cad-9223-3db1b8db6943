package content

import (
	"github.com/gin-gonic/gin"
	intraHandler "pxpat-backend/internal/content-cluster/video-service/intra/handler"
)

func RegisterContentInternalRoutes(r *gin.RouterGroup, handler *intraHandler.ContentHandler) {
	serviceGroup := r.Group("/intra/content")
	{
		// 微服务内部接口
		serviceGroup.POST("/updateStatus", handler.UpdateContentStatusForService)
		serviceGroup.GET("/:content_ksuid", handler.GetContentByContentKSUIDForService)
		serviceGroup.POST("/batch", handler.BatchGetContentsByKSUIDs)
	}

}
