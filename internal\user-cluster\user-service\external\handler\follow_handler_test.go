package handler

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
	"pxpat-backend/internal/user-cluster/user-service/dto"
	"pxpat-backend/pkg/errors"
)

// MockFollowService 模拟关注服务
type MockFollowService struct {
	mock.Mock
}

func (m *MockFollowService) FollowUser(ctx context.Context, followerKSUID string, req *dto.FollowUserRequest) (*dto.FollowUserResponse, error) {
	args := m.Called(ctx, followerKSUID, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dto.FollowUserResponse), args.Error(1)
}

func (m *MockFollowService) UnfollowUser(ctx context.Context, followerKSUID string, req *dto.UnfollowUserRequest) (*dto.FollowUserResponse, error) {
	args := m.Called(ctx, followerKSUID, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dto.FollowUserResponse), args.Error(1)
}

func (m *MockFollowService) CheckFollowStatus(ctx context.Context, followerKSUID string, req *dto.CheckFollowStatusRequest) (*dto.CheckFollowStatusResponse, error) {
	args := m.Called(ctx, followerKSUID, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dto.CheckFollowStatusResponse), args.Error(1)
}

func (m *MockFollowService) BatchCheckFollowStatus(ctx context.Context, followerKSUID string, req *dto.BatchCheckFollowRequest) (*dto.BatchCheckFollowResponse, error) {
	args := m.Called(ctx, followerKSUID, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dto.BatchCheckFollowResponse), args.Error(1)
}

func (m *MockFollowService) GetFollowStats(ctx context.Context, userKSUID string) (*dto.FollowStatsResponse, error) {
	args := m.Called(ctx, userKSUID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dto.FollowStatsResponse), args.Error(1)
}

func (m *MockFollowService) GetFollowers(ctx context.Context, currentUserKSUID string, req *dto.GetFollowersRequest) (*dto.GetFollowersResponse, error) {
	args := m.Called(ctx, currentUserKSUID, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dto.GetFollowersResponse), args.Error(1)
}

func (m *MockFollowService) GetFollowing(ctx context.Context, currentUserKSUID string, req *dto.GetFollowingRequest) (*dto.GetFollowingResponse, error) {
	args := m.Called(ctx, currentUserKSUID, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dto.GetFollowingResponse), args.Error(1)
}

// FollowHandlerTestSuite 关注处理器测试套件
type FollowHandlerTestSuite struct {
	suite.Suite
	handler     *FollowHandler
	mockService *MockFollowService
	router      *gin.Engine
}

func (suite *FollowHandlerTestSuite) SetupTest() {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	suite.mockService = new(MockFollowService)
	suite.handler = NewFollowHandler(suite.mockService)
	suite.router = gin.New()

	// 设置路由
	api := suite.router.Group("/api")
	api.POST("/follow/user", suite.handler.FollowUser)
	api.DELETE("/follow/user", suite.handler.UnfollowUser)
	api.GET("/follow/status", suite.handler.CheckFollowStatus)
	api.POST("/follow/status/batch", suite.handler.BatchCheckFollowStatus)
	api.GET("/follow/stats", suite.handler.GetFollowStats)
	api.GET("/users/followers", suite.handler.GetFollowers)
	api.GET("/users/following", suite.handler.GetFollowing)
	api.GET("/users/me/followers", suite.handler.GetMyFollowers)
	api.GET("/users/me/following", suite.handler.GetMyFollowing)
}

func (suite *FollowHandlerTestSuite) TestFollowUser_Success() {
	reqBody := dto.FollowUserRequest{
		FolloweeKSUID: "test_user_002",
	}

	expectedResponse := &dto.FollowUserResponse{
		Success: true,
		Message: "关注成功",
	}

	// 设置mock期望
	suite.mockService.On("FollowUser", mock.Anything, "test_user_001", mock.AnythingOfType("*dto.FollowUserRequest")).Return(expectedResponse, nil)

	// 准备请求
	jsonBody, _ := json.Marshal(reqBody)
	req := httptest.NewRequest(http.MethodPost, "/api/follow/user", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req
	c.Set("user_ksuid", "test_user_001")

	// 执行测试
	suite.handler.FollowUser(c)

	// 验证结果
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), float64(0), response["code"])

	// 验证mock调用
	suite.mockService.AssertExpectations(suite.T())
}

func (suite *FollowHandlerTestSuite) TestFollowUser_InvalidRequest() {
	// 准备无效请求（缺少必要字段）
	reqBody := map[string]interface{}{
		"invalid_field": "value",
	}

	jsonBody, _ := json.Marshal(reqBody)
	req := httptest.NewRequest(http.MethodPost, "/api/follow/user", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req
	c.Set("user_ksuid", "test_user_001")

	// 执行测试
	suite.handler.FollowUser(c)

	// 验证结果
	assert.Equal(suite.T(), http.StatusBadRequest, w.Code)
}

func (suite *FollowHandlerTestSuite) TestFollowUser_ServiceError() {
	reqBody := dto.FollowUserRequest{
		FolloweeKSUID: "test_user_002",
	}

	// 设置mock期望（返回错误）
	suite.mockService.On("FollowUser", mock.Anything, "test_user_001", mock.AnythingOfType("*dto.FollowUserRequest")).Return(nil, errors.NewBadRequestError("不能关注自己"))

	// 准备请求
	jsonBody, _ := json.Marshal(reqBody)
	req := httptest.NewRequest(http.MethodPost, "/api/follow/user", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req
	c.Set("user_ksuid", "test_user_001")

	// 执行测试
	suite.handler.FollowUser(c)

	// 验证结果
	assert.Equal(suite.T(), http.StatusBadRequest, w.Code)

	// 验证mock调用
	suite.mockService.AssertExpectations(suite.T())
}

func (suite *FollowHandlerTestSuite) TestUnfollowUser_Success() {
	reqBody := dto.UnfollowUserRequest{
		FolloweeKSUID: "test_user_002",
	}

	expectedResponse := &dto.FollowUserResponse{
		Success: true,
		Message: "取消关注成功",
	}

	// 设置mock期望
	suite.mockService.On("UnfollowUser", mock.Anything, "test_user_001", mock.AnythingOfType("*dto.UnfollowUserRequest")).Return(expectedResponse, nil)

	// 准备请求
	jsonBody, _ := json.Marshal(reqBody)
	req := httptest.NewRequest(http.MethodDelete, "/api/follow/user", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req
	c.Set("user_ksuid", "test_user_001")

	// 执行测试
	suite.handler.UnfollowUser(c)

	// 验证结果
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), float64(0), response["code"])

	// 验证mock调用
	suite.mockService.AssertExpectations(suite.T())
}

func (suite *FollowHandlerTestSuite) TestCheckFollowStatus_Success() {
	expectedResponse := &dto.CheckFollowStatusResponse{
		IsFollowing: true,
	}

	// 设置mock期望
	suite.mockService.On("CheckFollowStatus", mock.Anything, "test_user_001", mock.AnythingOfType("*dto.CheckFollowStatusRequest")).Return(expectedResponse, nil)

	// 准备请求
	req := httptest.NewRequest(http.MethodGet, "/api/follow/status?followee_ksuid=test_user_002", nil)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req
	c.Set("user_ksuid", "test_user_001")

	// 执行测试
	suite.handler.CheckFollowStatus(c)

	// 验证结果
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), float64(0), response["code"])

	// 验证mock调用
	suite.mockService.AssertExpectations(suite.T())
}

func (suite *FollowHandlerTestSuite) TestGetFollowStats_Success() {
	expectedResponse := &dto.FollowStatsResponse{
		FollowersCount: 10,
		FollowingCount: 5,
	}

	// 设置mock期望
	suite.mockService.On("GetFollowStats", mock.Anything, "test_user_001").Return(expectedResponse, nil)

	// 准备请求
	req := httptest.NewRequest(http.MethodGet, "/api/follow/stats", nil)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req
	c.Set("user_ksuid", "test_user_001")

	// 执行测试
	suite.handler.GetFollowStats(c)

	// 验证结果
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), float64(0), response["code"])

	// 验证mock调用
	suite.mockService.AssertExpectations(suite.T())
}

func (suite *FollowHandlerTestSuite) TestBatchCheckFollowStatus_Success() {
	reqBody := dto.BatchCheckFollowRequest{
		FolloweeKSUIDs: []string{"test_user_002", "test_user_003"},
	}

	expectedResponse := &dto.BatchCheckFollowResponse{
		FollowStatus: map[string]bool{
			"test_user_002": true,
			"test_user_003": false,
		},
	}

	// 设置mock期望
	suite.mockService.On("BatchCheckFollowStatus", mock.Anything, "test_user_001", mock.AnythingOfType("*dto.BatchCheckFollowRequest")).Return(expectedResponse, nil)

	// 准备请求
	jsonBody, _ := json.Marshal(reqBody)
	req := httptest.NewRequest(http.MethodPost, "/api/follow/status/batch", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req
	c.Set("user_ksuid", "test_user_001")

	// 执行测试
	suite.handler.BatchCheckFollowStatus(c)

	// 验证结果
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), float64(0), response["code"])

	// 验证mock调用
	suite.mockService.AssertExpectations(suite.T())
}

func (suite *FollowHandlerTestSuite) TestGetFollowers_Success() {
	expectedResponse := &dto.GetFollowersResponse{
		Followers: []dto.UserFollowInfo{
			{
				UserKSUID: "test_user_002",
				Nickname:  "用户2",
			},
		},
		Total: 1,
		Page:  1,
		PageSize: 10,
	}

	// 设置mock期望
	suite.mockService.On("GetFollowers", mock.Anything, "test_user_001", mock.AnythingOfType("*dto.GetFollowersRequest")).Return(expectedResponse, nil)

	// 准备请求
	req := httptest.NewRequest(http.MethodGet, "/api/users/followers?user_ksuid=test_user_002&page=1&page_size=10", nil)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req
	c.Set("user_ksuid", "test_user_001")

	// 执行测试
	suite.handler.GetFollowers(c)

	// 验证结果
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), float64(0), response["code"])

	// 验证mock调用
	suite.mockService.AssertExpectations(suite.T())
}

func (suite *FollowHandlerTestSuite) TestGetFollowing_Success() {
	expectedResponse := &dto.GetFollowingResponse{
		Following: []dto.UserFollowInfo{
			{
				UserKSUID: "test_user_002",
				Nickname:  "用户2",
			},
		},
		Total: 1,
		Page:  1,
		PageSize: 10,
	}

	// 设置mock期望
	suite.mockService.On("GetFollowing", mock.Anything, "test_user_001", mock.AnythingOfType("*dto.GetFollowingRequest")).Return(expectedResponse, nil)

	// 准备请求
	req := httptest.NewRequest(http.MethodGet, "/api/users/following?user_ksuid=test_user_002&page=1&page_size=10", nil)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req
	c.Set("user_ksuid", "test_user_001")

	// 执行测试
	suite.handler.GetFollowing(c)

	// 验证结果
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), float64(0), response["code"])

	// 验证mock调用
	suite.mockService.AssertExpectations(suite.T())
}

func (suite *FollowHandlerTestSuite) TestGetMyFollowers_Success() {
	expectedResponse := &dto.GetFollowersResponse{
		Followers: []dto.UserFollowInfo{
			{
				UserKSUID: "test_user_002",
				Nickname:  "用户2",
			},
		},
		Total: 1,
		Page:  1,
		PageSize: 10,
	}

	// 设置mock期望
	suite.mockService.On("GetFollowers", mock.Anything, "test_user_001", mock.AnythingOfType("*dto.GetFollowersRequest")).Return(expectedResponse, nil)

	// 准备请求
	req := httptest.NewRequest(http.MethodGet, "/api/users/me/followers?page=1&page_size=10", nil)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req
	c.Set("user_ksuid", "test_user_001")

	// 执行测试
	suite.handler.GetMyFollowers(c)

	// 验证结果
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), float64(0), response["code"])

	// 验证mock调用
	suite.mockService.AssertExpectations(suite.T())
}

func (suite *FollowHandlerTestSuite) TestGetMyFollowing_Success() {
	expectedResponse := &dto.GetFollowingResponse{
		Following: []dto.UserFollowInfo{
			{
				UserKSUID: "test_user_002",
				Nickname:  "用户2",
			},
		},
		Total: 1,
		Page:  1,
		PageSize: 10,
	}

	// 设置mock期望
	suite.mockService.On("GetFollowing", mock.Anything, "test_user_001", mock.AnythingOfType("*dto.GetFollowingRequest")).Return(expectedResponse, nil)

	// 准备请求
	req := httptest.NewRequest(http.MethodGet, "/api/users/me/following?page=1&page_size=10", nil)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req
	c.Set("user_ksuid", "test_user_001")

	// 执行测试
	suite.handler.GetMyFollowing(c)

	// 验证结果
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), float64(0), response["code"])

	// 验证mock调用
	suite.mockService.AssertExpectations(suite.T())
}

func (suite *FollowHandlerTestSuite) TestMissingUserKSUID() {
	reqBody := dto.FollowUserRequest{
		FolloweeKSUID: "test_user_002",
	}

	// 准备请求（不设置user_ksuid）
	jsonBody, _ := json.Marshal(reqBody)
	req := httptest.NewRequest(http.MethodPost, "/api/follow/user", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req
	// 不设置user_ksuid

	// 执行测试
	suite.handler.FollowUser(c)

	// 验证结果
	assert.Equal(suite.T(), http.StatusUnauthorized, w.Code)
}

func TestFollowHandlerTestSuite(t *testing.T) {
	suite.Run(t, new(FollowHandlerTestSuite))
}
