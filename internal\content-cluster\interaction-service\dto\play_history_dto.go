package dto

import (
	"time"
)

// UpdatePlayHistoryRequest 更新播放历史记录请求
type UpdatePlayHistoryRequest struct {
	UserKSUID    string `json:"-"`                                                       // 用户KSUID
	ContentKSUID string `json:"content_ksuid" binding:"required"`                        // 内容KSUID
	ContentType  string `json:"content_type" binding:"required,oneof=video anime short"` // 内容类型：video/anime/short
	PlayDuration int64  `json:"play_duration" binding:"required"`                        // 播放时长（秒）
}

// UpdatePlayHistoryResponse 更新播放历史记录响应
type UpdatePlayHistoryResponse struct {
	PlayHistoryItemID string `json:"play_history_item_id"` // 播放历史记录ID
	UserKSUID         string `json:"user_ksuid"`           // 用户KSUID
	ContentKSUID      string `json:"content_ksuid"`        // 内容KSUID
	ContentType       string `json:"content_type"`         // 内容类型
	PlayDuration      int64  `json:"play_duration"`        // 播放时长（秒）
	IsNew             bool   `json:"is_new"`               // 是否为新创建的记录
}

// GetMyPlayHistoryRequest 获取我的播放历史记录请求
type GetMyPlayHistoryRequest struct {
	Page        int    `form:"page" binding:"required,min=1" json:"page" example:"1"`                        // 页码，从1开始
	PageSize    int    `form:"page_size" json:"page_size" binding:"required,min=1,max=50" example:"20"`      // 每页数量，默认20
	ContentType string `form:"content_type" json:"content_type" binding:"omitempty,oneof=video anime short"` // 内容类型过滤：video/anime/short，为空则获取所有
}

// PlayHistoryItemDTO 播放历史记录项DTO
type PlayHistoryItemDTO struct {
	PlayHistoryItemID string               `json:"play_history_item_id" example:"01ARZ3NDEKTSV4RRFFQ69G5FAV"` // 播放历史记录ID
	ContentKSUID      string               `json:"content_ksuid" example:"01ARZ3NDEKTSV4RRFFQ69G5FAV"`        // 内容KSUID
	ContentType       string               `json:"content_type" example:"video"`                              // 内容类型
	PlayDuration      int64                `json:"play_duration" example:"120"`                               // 播放时长（秒）
	ContentInfo       *ExternalContentInfo `json:"content_info,omitempty"`                                    // 内容信息（从video-service获取）
	CreatedAt         time.Time            `json:"created_at" example:"2023-01-01T00:00:00Z"`                 // 创建时间
	UpdatedAt         time.Time            `json:"updated_at" example:"2023-01-01T00:00:00Z"`                 // 更新时间
}

// ExternalContentInfo 外部内容信息（从video-service获取）
type ExternalContentInfo struct {
	ContentKSUID string         `json:"content_ksuid"`
	Title        string         `json:"title"`
	Description  string         `json:"description"`
	CoverURL     string         `json:"cover_url"`
	Duration     int64          `json:"duration"`
	UserInfo     *UserBasicInfo `json:"user_info,omitempty"`
}

// UserBasicInfo 用户基础信息
type UserBasicInfo struct {
	UserKSUID   string `json:"user_ksuid"`
	Username    string `json:"username"`
	Nickname    string `json:"nickname"`
	Avatar      string `json:"avatar"`
	Bio         string `json:"bio"`
	HideComment bool   `json:"hide_comment"`
}

// GetMyPlayHistoryResponse 获取我的播放历史记录响应
type GetMyPlayHistoryResponse struct {
	PlayHistories []PlayHistoryItemDTO `json:"play_histories"`           // 播放历史记录列表
	Total         int64                `json:"total" example:"100"`      // 总数
	Page          int                  `json:"page" example:"1"`         // 当前页码
	PageSize      int                  `json:"page_size" example:"20"`   // 每页数量
	TotalPages    int                  `json:"total_pages" example:"5"`  // 总页数
	HasNext       bool                 `json:"has_next" example:"true"`  // 是否有下一页
	HasPrev       bool                 `json:"has_prev" example:"false"` // 是否有上一页
}

// DeletePlayHistoryRequest 删除指定播放历史记录请求
type DeletePlayHistoryRequest struct {
	PlayHistoryItemIDs []string `json:"play_history_item_ids" binding:"required,min=1,dive,min=1" example:"[\"01ARZ3NDEKTSV4RRFFQ69G5FAV\",\"01ARZ3NDEKTSV4RRFFQ69G5FB\"]"` // 要删除的播放历史记录ID数组
}

// DeletePlayHistoryResponse 删除指定播放历史记录响应
type DeletePlayHistoryResponse struct {
	DeletedCount int64 `json:"deleted_count" example:"2"` // 删除的记录数量
}

// InternalUpdatePlayHistoryRequest 内部更新播放历史记录请求
type InternalUpdatePlayHistoryRequest struct {
	UserKSUID    string `json:"user_ksuid" binding:"required"`                           // 用户KSUID
	ContentKSUID string `json:"content_ksuid" binding:"required"`                        // 内容KSUID
	ContentType  string `json:"content_type" binding:"required,oneof=video anime short"` // 内容类型：video/anime/short
	PlayDuration int64  `json:"play_duration" binding:"required"`                        // 播放时长（秒）
}

// InternalUpdatePlayHistoryResponse 内部更新播放历史记录响应
type InternalUpdatePlayHistoryResponse struct {
	PlayHistoryItemID string `json:"play_history_item_id"` // 播放历史记录ID
	UserKSUID         string `json:"user_ksuid"`           // 用户KSUID
	ContentKSUID      string `json:"content_ksuid"`        // 内容KSUID
	ContentType       string `json:"content_type"`         // 内容类型
	PlayDuration      int64  `json:"play_duration"`        // 播放时长（秒）
	IsNew             bool   `json:"is_new"`               // 是否为新创建的记录
}

// InternalGetPlayHistoryRequest 内部获取播放历史记录请求
type InternalGetPlayHistoryRequest struct {
	UserKSUID    string `json:"user_ksuid" binding:"required"`    // 用户KSUID
	ContentKSUID string `json:"content_ksuid" binding:"required"` // 内容KSUID
}

// InternalGetPlayHistoryResponse 内部获取播放历史记录响应
type InternalGetPlayHistoryResponse struct {
	PlayHistoryItemID string    `json:"play_history_item_id"` // 播放历史记录ID
	UserKSUID         string    `json:"user_ksuid"`           // 用户KSUID
	ContentKSUID      string    `json:"content_ksuid"`        // 内容KSUID
	ContentType       string    `json:"content_type"`         // 内容类型
	PlayDuration      int64     `json:"play_duration"`        // 播放时长（秒）
	CreatedAt         time.Time `json:"created_at"`           // 创建时间
	UpdatedAt         time.Time `json:"updated_at"`           // 更新时间
	Found             bool      `json:"found"`                // 是否找到记录
}
