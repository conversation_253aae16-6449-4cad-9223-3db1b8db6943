package handler

import (
	"net/http"
	"pxpat-backend/internal/user-cluster/user-service/dto"
	"pxpat-backend/internal/user-cluster/user-service/external/service"
	"pxpat-backend/pkg/errors"
	"pxpat-backend/pkg/middleware/tracing"
	globalTypes "pxpat-backend/pkg/types"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
)

// FollowHandler 关注功能HTTP处理器
type FollowHandler struct {
	followService *service.FollowService
}

// NewFollowHandler 创建关注处理器实例
func NewFollowHandler(followService *service.FollowService) *FollowHandler {
	return &FollowHandler{
		followService: followService,
	}
}

// FollowUser 关注用户
func (h *FollowHandler) FollowUser(c *gin.Context) {
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "FollowUser")
	defer span.End()

	// 获取当前用户信息
	currentUser, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, globalTypes.ErrorResponse{
			Code:    "UNAUTHORIZED",
			Message: "用户未登录",
		})
		return
	}

	userInfo, ok := currentUser.(map[string]interface{})
	if !ok {
		c.JSON(http.StatusInternalServerError, globalTypes.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "用户信息格式错误",
		})
		return
	}

	currentUserKSUID, ok := userInfo["user_ksuid"].(string)
	if !ok || currentUserKSUID == "" {
		c.JSON(http.StatusInternalServerError, globalTypes.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "无法获取用户标识",
		})
		return
	}

	// 解析请求参数
	var req dto.FollowUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, globalTypes.ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "请求参数格式错误",
		})
		return
	}

	// 调用服务层
	response, err := h.followService.FollowUser(ctx, currentUserKSUID, req.FolloweeKSUID)
	if err != nil {
		log.Error().Err(err).Str("follower_ksuid", currentUserKSUID).Str("followee_ksuid", req.FolloweeKSUID).Msg("关注用户失败")

		if appErr, ok := err.(*errors.AppError); ok {
			c.JSON(appErr.HTTPStatus, globalTypes.ErrorResponse{
				Code:    appErr.Code,
				Message: appErr.Message,
			})
			return
		}

		c.JSON(http.StatusInternalServerError, globalTypes.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "关注用户失败",
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.SuccessResponse{
		Code:    "SUCCESS",
		Message: "操作成功",
		Data:    response,
	})
}

// UnfollowUser 取消关注用户
func (h *FollowHandler) UnfollowUser(c *gin.Context) {
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "UnfollowUser")
	defer span.End()

	// 获取当前用户信息
	currentUser, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, globalTypes.ErrorResponse{
			Code:    "UNAUTHORIZED",
			Message: "用户未登录",
		})
		return
	}

	userInfo, ok := currentUser.(map[string]interface{})
	if !ok {
		c.JSON(http.StatusInternalServerError, globalTypes.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "用户信息格式错误",
		})
		return
	}

	currentUserKSUID, ok := userInfo["user_ksuid"].(string)
	if !ok || currentUserKSUID == "" {
		c.JSON(http.StatusInternalServerError, globalTypes.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "无法获取用户标识",
		})
		return
	}

	// 解析请求参数
	var req dto.UnfollowUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, globalTypes.ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "请求参数格式错误",
		})
		return
	}

	// 调用服务层
	response, err := h.followService.UnfollowUser(ctx, currentUserKSUID, req.FolloweeKSUID)
	if err != nil {
		log.Error().Err(err).Str("follower_ksuid", currentUserKSUID).Str("followee_ksuid", req.FolloweeKSUID).Msg("取消关注用户失败")

		if appErr, ok := err.(*errors.AppError); ok {
			c.JSON(appErr.HTTPStatus, globalTypes.ErrorResponse{
				Code:    appErr.Code,
				Message: appErr.Message,
			})
			return
		}

		c.JSON(http.StatusInternalServerError, globalTypes.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "取消关注用户失败",
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.SuccessResponse{
		Code:    "SUCCESS",
		Message: "操作成功",
		Data:    response,
	})
}

// GetFollowers 获取粉丝列表
func (h *FollowHandler) GetFollowers(c *gin.Context) {
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "GetFollowers")
	defer span.End()

	// 解析请求参数
	var req dto.GetFollowersRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, globalTypes.ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "请求参数格式错误",
		})
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	// 获取当前用户信息（如果已登录）
	var currentUserKSUID string
	currentUser, exists := c.Get("user")
	if exists {
		userInfo, ok := currentUser.(map[string]interface{})
		if ok {
			if ksuid, ok := userInfo["user_ksuid"].(string); ok {
				currentUserKSUID = ksuid
			}
		}
	}

	// 确定要查询的用户KSUID
	userKSUID := req.UserKSUID
	if userKSUID == "" {
		// 如果没有指定用户KSUID，则获取当前用户的粉丝
		if currentUserKSUID == "" {
			c.JSON(http.StatusUnauthorized, globalTypes.ErrorResponse{
				Code:    "UNAUTHORIZED",
				Message: "用户未登录",
			})
			return
		}
		userKSUID = currentUserKSUID
	}

	// 检查隐私权限
	if userKSUID != currentUserKSUID {
		// 查看其他用户的粉丝列表，需要检查隐私设置
		response, err := h.followService.CheckFollowersPrivacy(ctx, userKSUID)
		if err != nil {
			log.Error().Err(err).Str("user_ksuid", userKSUID).Msg("检查粉丝列表隐私设置失败")

			if appErr, ok := err.(*errors.AppError); ok {
				c.JSON(appErr.HTTPStatus, globalTypes.ErrorResponse{
					Code:    appErr.Code,
					Message: appErr.Message,
				})
				return
			}

			c.JSON(http.StatusInternalServerError, globalTypes.ErrorResponse{
				Code:    "INTERNAL_ERROR",
				Message: "检查权限失败",
			})
			return
		}

		if !response {
			c.JSON(http.StatusForbidden, globalTypes.ErrorResponse{
				Code:    "PERMISSION_DENIED",
				Message: "该用户的粉丝列表不公开",
			})
			return
		}
	}

	// 调用服务层
	response, err := h.followService.GetFollowers(ctx, userKSUID, req.Page, req.PageSize)
	if err != nil {
		log.Error().Err(err).Str("user_ksuid", userKSUID).Msg("获取粉丝列表失败")

		if appErr, ok := err.(*errors.AppError); ok {
			c.JSON(appErr.HTTPStatus, globalTypes.ErrorResponse{
				Code:    appErr.Code,
				Message: appErr.Message,
			})
			return
		}

		c.JSON(http.StatusInternalServerError, globalTypes.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "获取粉丝列表失败",
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.SuccessResponse{
		Code:    "SUCCESS",
		Message: "获取成功",
		Data:    response,
	})
}

// GetFollowing 获取关注列表
func (h *FollowHandler) GetFollowing(c *gin.Context) {
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "GetFollowing")
	defer span.End()

	// 解析请求参数
	var req dto.GetFollowingRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, globalTypes.ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "请求参数格式错误",
		})
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	// 获取当前用户信息（如果已登录）
	var currentUserKSUID string
	currentUser, exists := c.Get("user")
	if exists {
		userInfo, ok := currentUser.(map[string]interface{})
		if ok {
			if ksuid, ok := userInfo["user_ksuid"].(string); ok {
				currentUserKSUID = ksuid
			}
		}
	}

	// 确定要查询的用户KSUID
	userKSUID := req.UserKSUID
	if userKSUID == "" {
		// 如果没有指定用户KSUID，则获取当前用户的关注
		if currentUserKSUID == "" {
			c.JSON(http.StatusUnauthorized, globalTypes.ErrorResponse{
				Code:    "UNAUTHORIZED",
				Message: "用户未登录",
			})
			return
		}
		userKSUID = currentUserKSUID
	}

	// 检查隐私权限
	if userKSUID != currentUserKSUID {
		// 查看其他用户的关注列表，需要检查隐私设置
		response, err := h.followService.CheckFollowingPrivacy(ctx, userKSUID)
		if err != nil {
			log.Error().Err(err).Str("user_ksuid", userKSUID).Msg("检查关注列表隐私设置失败")

			if appErr, ok := err.(*errors.AppError); ok {
				c.JSON(appErr.HTTPStatus, globalTypes.ErrorResponse{
					Code:    appErr.Code,
					Message: appErr.Message,
				})
				return
			}

			c.JSON(http.StatusInternalServerError, globalTypes.ErrorResponse{
				Code:    "INTERNAL_ERROR",
				Message: "检查权限失败",
			})
			return
		}

		if !response {
			c.JSON(http.StatusForbidden, globalTypes.ErrorResponse{
				Code:    "PERMISSION_DENIED",
				Message: "该用户的关注列表不公开",
			})
			return
		}
	}

	// 调用服务层
	response, err := h.followService.GetFollowing(ctx, userKSUID, req.Page, req.PageSize)
	if err != nil {
		log.Error().Err(err).Str("user_ksuid", userKSUID).Msg("获取关注列表失败")

		if appErr, ok := err.(*errors.AppError); ok {
			c.JSON(appErr.HTTPStatus, globalTypes.ErrorResponse{
				Code:    appErr.Code,
				Message: appErr.Message,
			})
			return
		}

		c.JSON(http.StatusInternalServerError, globalTypes.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "获取关注列表失败",
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.SuccessResponse{
		Code:    "SUCCESS",
		Message: "获取成功",
		Data:    response,
	})
}

// CheckFollowStatus 检查关注状态
func (h *FollowHandler) CheckFollowStatus(c *gin.Context) {
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "CheckFollowStatus")
	defer span.End()

	// 获取当前用户信息
	currentUser, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, globalTypes.ErrorResponse{
			Code:    "UNAUTHORIZED",
			Message: "用户未登录",
		})
		return
	}

	userInfo, ok := currentUser.(map[string]interface{})
	if !ok {
		c.JSON(http.StatusInternalServerError, globalTypes.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "用户信息格式错误",
		})
		return
	}

	currentUserKSUID, ok := userInfo["user_ksuid"].(string)
	if !ok || currentUserKSUID == "" {
		c.JSON(http.StatusInternalServerError, globalTypes.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "无法获取用户标识",
		})
		return
	}

	// 解析请求参数
	var req dto.CheckFollowStatusRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, globalTypes.ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "请求参数格式错误",
		})
		return
	}

	// 调用服务层
	response, err := h.followService.CheckFollowStatus(ctx, currentUserKSUID, req.FolloweeKSUID)
	if err != nil {
		log.Error().Err(err).Str("current_user", currentUserKSUID).Str("target_user", req.FolloweeKSUID).Msg("检查关注状态失败")

		if appErr, ok := err.(*errors.AppError); ok {
			c.JSON(appErr.HTTPStatus, globalTypes.ErrorResponse{
				Code:    appErr.Code,
				Message: appErr.Message,
			})
			return
		}

		c.JSON(http.StatusInternalServerError, globalTypes.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "检查关注状态失败",
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.SuccessResponse{
		Code:    "SUCCESS",
		Message: "获取成功",
		Data:    response,
	})
}

// GetFollowStats 获取关注统计信息
func (h *FollowHandler) GetFollowStats(c *gin.Context) {
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "GetFollowStats")
	defer span.End()

	// 获取用户KSUID参数
	userKSUID := c.Query("user_ksuid")

	// 如果没有指定用户KSUID，则获取当前用户的统计
	if userKSUID == "" {
		currentUser, exists := c.Get("user")
		if !exists {
			c.JSON(http.StatusUnauthorized, globalTypes.ErrorResponse{
				Code:    "UNAUTHORIZED",
				Message: "用户未登录",
			})
			return
		}

		userInfo, ok := currentUser.(map[string]interface{})
		if !ok {
			c.JSON(http.StatusInternalServerError, globalTypes.ErrorResponse{
				Code:    "INTERNAL_ERROR",
				Message: "用户信息格式错误",
			})
			return
		}

		currentUserKSUID, ok := userInfo["user_ksuid"].(string)
		if !ok || currentUserKSUID == "" {
			c.JSON(http.StatusInternalServerError, globalTypes.ErrorResponse{
				Code:    "INTERNAL_ERROR",
				Message: "无法获取用户标识",
			})
			return
		}
		userKSUID = currentUserKSUID
	}

	// 调用服务层
	response, err := h.followService.GetFollowStats(ctx, userKSUID)
	if err != nil {
		log.Error().Err(err).Str("user_ksuid", userKSUID).Msg("获取关注统计失败")

		if appErr, ok := err.(*errors.AppError); ok {
			c.JSON(appErr.HTTPStatus, globalTypes.ErrorResponse{
				Code:    appErr.Code,
				Message: appErr.Message,
			})
			return
		}

		c.JSON(http.StatusInternalServerError, globalTypes.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "获取关注统计失败",
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.SuccessResponse{
		Code:    "SUCCESS",
		Message: "获取成功",
		Data:    response,
	})
}

// BatchCheckFollowStatus 批量检查关注状态
func (h *FollowHandler) BatchCheckFollowStatus(c *gin.Context) {
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "BatchCheckFollowStatus")
	defer span.End()

	// 获取当前用户信息
	currentUser, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, globalTypes.ErrorResponse{
			Code:    "UNAUTHORIZED",
			Message: "用户未登录",
		})
		return
	}

	userInfo, ok := currentUser.(map[string]interface{})
	if !ok {
		c.JSON(http.StatusInternalServerError, globalTypes.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "用户信息格式错误",
		})
		return
	}

	currentUserKSUID, ok := userInfo["user_ksuid"].(string)
	if !ok || currentUserKSUID == "" {
		c.JSON(http.StatusInternalServerError, globalTypes.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "无法获取用户标识",
		})
		return
	}

	// 解析请求参数
	var req dto.BatchCheckFollowRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, globalTypes.ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "请求参数格式错误",
		})
		return
	}

	// 调用服务层
	response, err := h.followService.BatchCheckFollowStatus(ctx, currentUserKSUID, req.UserKSUIDs)
	if err != nil {
		log.Error().Err(err).Str("current_user", currentUserKSUID).Msg("批量检查关注状态失败")

		if appErr, ok := err.(*errors.AppError); ok {
			c.JSON(appErr.HTTPStatus, globalTypes.ErrorResponse{
				Code:    appErr.Code,
				Message: appErr.Message,
			})
			return
		}

		c.JSON(http.StatusInternalServerError, globalTypes.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "批量检查关注状态失败",
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.SuccessResponse{
		Code:    "SUCCESS",
		Message: "获取成功",
		Data:    response,
	})
}
