# GetMyContents API 内容类型筛选功能

## 概述

`GetMyContents` API 现在支持按内容类型筛选用户自己发布的内容。

## 接口地址

```
GET /api/content/my
```

## 请求参数

| 参数名 | 类型 | 必填 | 说明 | 默认值 | 示例 |
|--------|------|------|------|--------|------|
| page | int | 否 | 页码，从1开始 | 1 | 1 |
| page_size | int | 否 | 每页数量，最大100 | 20 | 20 |
| content_type | string | 否 | 内容类型过滤 | 空（获取所有） | video |

### content_type 支持的值

| 值 | 说明 |
|----|------|
| video | 视频内容 |
| anime | 动漫内容 |
| short_video | 短视频内容 |
| 空值 | 获取所有类型的内容 |

## 请求示例

### 获取所有类型的内容
```bash
curl -X GET "http://localhost:8080/api/content/my?page=1&page_size=20" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 只获取视频内容
```bash
curl -X GET "http://localhost:8080/api/content/my?page=1&page_size=20&content_type=video" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 只获取动漫内容
```bash
curl -X GET "http://localhost:8080/api/content/my?page=1&page_size=20&content_type=anime" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 只获取短视频内容
```bash
curl -X GET "http://localhost:8080/api/content/my?page=1&page_size=20&content_type=short_video" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 响应格式

```json
{
  "code": 200,
  "data": {
    "contents": [
      {
        "user_ksuid": "01ARZ3NDEKTSV4RRFFQ69G5FAV",
        "content_ksuid": "01ARZ3NDEKTSV4RRFFQ69G5FAV",
        "title": "示例视频标题",
        "video_id": "V001",
        "cover_url": "https://example.com/cover.jpg",
        "created_at": "2023-01-01T00:00:00Z",
        "view_count": 1000,
        "like_count": 100,
        "dislike_count": 5,
        "share_count": 20,
        "favorite_count": 50,
        "comment_count": 30,
        "complaint_count": 0,
        "duration": 300,
        "orientation": "landscape",
        "status": "published",
        "type": "video",
        "user_info": {
          "user_ksuid": "01ARZ3NDEKTSV4RRFFQ69G5FAV",
          "username": "example_user",
          "nickname": "示例用户",
          "avatar_url": "https://example.com/avatar.jpg"
        }
      }
    ],
    "total": 100,
    "page": 1,
    "page_size": 20,
    "total_pages": 5,
    "has_next": true,
    "has_prev": false
  }
}
```

## 错误响应

### 无效的内容类型
```json
{
  "code": 400,
  "message": "Invalid parameter"
}
```

## 实现细节

### 数据库查询
当指定 `content_type` 参数时，系统会在数据库查询中添加 `WHERE type = ?` 条件：

```sql
SELECT * FROM contents 
WHERE user_ksuid = ? 
  AND type = ?  -- 当指定content_type时添加此条件
ORDER BY created_at DESC 
LIMIT ? OFFSET ?
```

### 日志记录
所有请求都会记录详细的日志信息，包括：
- 用户KSUID
- 页码和页面大小
- 内容类型筛选条件
- 查询结果统计

## 注意事项

1. `content_type` 参数是可选的，不传递时返回所有类型的内容
2. 内容类型值必须是预定义的枚举值之一
3. 筛选只影响当前用户自己发布的内容
4. 分页逻辑在筛选后应用
