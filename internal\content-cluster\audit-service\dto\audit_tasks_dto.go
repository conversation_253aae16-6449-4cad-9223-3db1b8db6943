package dto

import (
	"pxpat-backend/internal/user-cluster/user-service/dto"
)

type AuditTasksRequest struct {
	UserKSUID    string
	ContentKSUID string `json:"content_ksuid" binding:"required"`
	ContentType  string `json:"content_type" binding:"required"`
}

// InternalAuditTaskRequest 内部服务创建审核任务请求（用于novel-service等调用）
type InternalAuditTaskRequest struct {
	ContentKSUID string `json:"content_ksuid" binding:"required"`
	ContentType  string `json:"content_type" binding:"required"` // video, novel, chapter, comment等
	NeedVotes    int    `json:"need_votes,omitempty"`
}

// AuditVoteRequest 审核投票请求
type AuditVoteRequest struct {
	UserKSUID string
	TaskID    uint64 `json:"task_id" binding:"required"`
	Reason    string `json:"reason" binding:"required"`
	Level     string `json:"level" binding:"required"`
	VoteType  string `json:"vote_type" binding:"required,oneof=permit reject"` // permit: 通过, reject: 拒绝
}

// AuditVoteResponse 审核投票响应
type AuditVoteResponse struct {
	TaskID      uint64 `json:"task_id"`
	Status      string `json:"status"`       // pending, permit, reject
	Votes       int    `json:"votes"`        // 当前总投票数
	PermitNum   int    `json:"permit_num"`   // 通过票数
	RejectNum   int    `json:"reject_num"`   // 拒绝票数
	NeedVotes   int    `json:"need_votes"`   // 需要的总票数
	IsCompleted bool   `json:"is_completed"` // 是否已完成审核
}

// GetRandomAuditTaskRequest 随机获取审核任务请求
type GetRandomAuditTaskRequest struct {
	UserKSUID   string
	ContentType string `json:"content_type" binding:"required,oneof=video anime"` // 内容类型
}

// AuditorLogItem 审核员记录项
type AuditorLogItem struct {
	ID         uint64    `json:"id"`
	UserKSUID  string    `json:"user_ksuid"`
	VoteType   string    `json:"vote_type"`
	VoteWeight int       `json:"vote_weight"`
	Reason     string    `json:"reason"`
	CreatedAt  string    `json:"created_at"`
	UserInfo   *UserInfo `json:"user_info,omitempty"` // 用户信息
}

// UserInfo 用户基本信息（使用user-service的SimpleUserModel）
type UserInfo = dto.SimpleUserModel

// GetRandomAuditTaskResponse 随机获取审核任务响应
type GetRandomAuditTaskResponse struct {
	TaskID       uint64           `json:"task_id"`
	ContentKSUID string           `json:"content_ksuid"`
	ContentType  string           `json:"content_type"`
	Round        int              `json:"round"`
	Votes        int              `json:"votes"`
	PermitNum    int              `json:"permit_num"`
	RejectNum    int              `json:"reject_num"`
	NeedVotes    int              `json:"need_votes"`
	CreatedAt    string           `json:"created_at"`
	AuditorLogs  []AuditorLogItem `json:"auditor_logs,omitempty"`
}

// GetPendingAuditTasksRequest 获取待审核任务列表请求
type GetPendingAuditTasksRequest struct {
	UserKSUID   string
	ContentType string `json:"content_type,omitempty" binding:"required,oneof=video short_video anime manga novel music article drama"` // 可选的内容类型筛选，支持：video, short_video, anime, manga, novel, music, article, drama
	Page        int    `json:"page" binding:"min=1"`                                                                                    // 页码，从1开始
	PageSize    int    `json:"page_size" binding:"min=1,max=25"`                                                                        // 每页数量，最大25条
}

// PendingAuditTaskItem 待审核任务列表项
type PendingAuditTaskItem struct {
	TaskID       uint64           `json:"task_id"`
	ContentKSUID string           `json:"content_ksuid"`
	ContentType  string           `json:"content_type"`
	Round        int              `json:"round"`
	Votes        int              `json:"votes"`
	PermitNum    int              `json:"permit_num"`
	RejectNum    int              `json:"reject_num"`
	NeedVotes    int              `json:"need_votes"`
	CreatedAt    string           `json:"created_at"`
	AuditorLogs  []AuditorLogItem `json:"auditor_logs,omitempty"`
}

// GetPendingAuditTasksResponse 获取待审核任务列表响应
type GetPendingAuditTasksResponse struct {
	Tasks      []PendingAuditTaskItem `json:"tasks"`
	Total      int64                  `json:"total"`       // 总数量
	Page       int                    `json:"page"`        // 当前页码
	PageSize   int                    `json:"page_size"`   // 每页数量
	TotalPages int                    `json:"total_pages"` // 总页数
}

// GetAuditTaskByIDRequest 根据ID获取审核任务请求
type GetAuditTaskByIDRequest struct {
	TaskID uint64 `uri:"task_id" binding:"required"` // 任务ID
}

// GetAuditTaskByIDResponse 根据ID获取审核任务响应
type GetAuditTaskByIDResponse struct {
	TaskID      uint64           `json:"task_id"`
	VideoKSUID  string           `json:"video_ksuid"`
	ContentType string           `json:"content_type"`
	Status      string           `json:"status"`
	Round       int              `json:"round"`
	Votes       int              `json:"votes"`
	PermitNum   int              `json:"permit_num"`
	RejectNum   int              `json:"reject_num"`
	NeedVotes   int              `json:"need_votes"`
	CreatedAt   string           `json:"created_at"`
	UpdatedAt   string           `json:"updated_at"`
	AuditorLogs []AuditorLogItem `json:"auditor_logs"`
}

// AuditOKEvent 审核通过事件
type AuditOKEvent struct {
	ContentKSUID string  `json:"content_ksuid"` // 内容ID
	AuditTaskID  uint64  `json:"audit_task_id"` // 审核任务ID
	UserKSUID    string  `json:"user_ksuid"`    // 内容创建者ID
	FileKSUID    string  `json:"file_ksuid"`    // 文件ID
	ContentType  string  `json:"content_type"`  // 内容类型
	Level        string  `json:"level"`         // 视频等级
	PermitRate   float64 `json:"permit_rate"`   // 通过率
	TotalVotes   int     `json:"total_votes"`   // 总票数
	PermitVotes  int     `json:"permit_votes"`  // 通过票数
	RejectVotes  int     `json:"reject_votes"`  // 拒绝票数
}
