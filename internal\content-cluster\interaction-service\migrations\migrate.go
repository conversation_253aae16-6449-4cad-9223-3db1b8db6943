package migrations

import (
	"fmt"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
	model2 "pxpat-backend/internal/content-cluster/interaction-service/model"
)

func AutoMigrate(db *gorm.DB) {
	log.Info().Msg("Starting album service database migration")

	err := db.AutoMigrate(
		model2.Album{},
		model2.AlbumContent{},
		// Favorite相关模型
		&model2.FavoriteFolder{},
	)
	if err != nil {
		log.Fatal().
			Err(err).
			Msg("Failed to migrate album service models")
		panic(fmt.Errorf("迁移模型失败: %w", err))
	}

	log.Info().Msg("Album service models migrated successfully")

	// 创建收藏项分表
	err = createFavoriteItemTables(db)
	if err != nil {
		log.Fatal().
			Err(err).
			Msg("Failed to create favorite item tables")
		panic(fmt.Errorf("创建收藏项分表失败: %w", err))
	}
	log.Info().Msg("Favorite item tables created successfully")

	// 创建点赞分表
	err = createLikeTables(db)
	if err != nil {
		log.Fatal().
			Err(err).
			Msg("Failed to create like tables")
		panic(fmt.Errorf("创建点赞分表失败: %w", err))
	}
	log.Info().Msg("Like tables created successfully")

	// 创建点赞统计表
	err = createLikeStatsTable(db)
	if err != nil {
		log.Fatal().
			Err(err).
			Msg("Failed to create like stats table")
		panic(fmt.Errorf("创建点赞统计表失败: %w", err))
	}
	log.Info().Msg("Like stats table created successfully")

	// 创建收藏统计表
	err = createFavoriteStatsTable(db)
	if err != nil {
		log.Fatal().
			Err(err).
			Msg("Failed to create favorite stats table")
		panic(fmt.Errorf("创建收藏统计表失败: %w", err))
	}
	log.Info().Msg("Favorite stats table created successfully")

	// 创建播放历史分表
	err = createPlayHistoryItemTables(db)
	if err != nil {
		log.Fatal().
			Err(err).
			Msg("Failed to create play history item tables")
		panic(fmt.Errorf("创建播放历史分表失败: %w", err))
	}
	log.Info().Msg("Play history item tables created successfully")

	err = Init(db)
	if err != nil {
		log.Fatal().
			Err(err).
			Msg("Failed to initialize album service database constraints")
		panic(err.Error())
	}

	log.Info().Msg("Album service database migration completed successfully")
}

func Init(db *gorm.DB) error {
	log.Debug().Msg("Initializing album service database constraints")

	if exist := db.Migrator().HasTable("albums"); !exist {
		log.Error().Msg("albums table does not exist")
		return fmt.Errorf("albums 表不存在")
	}
	log.Debug().Msg("albums table exists")

	// 检查user_users表是否存在，如果不存在则跳过外键约束
	if exist := db.Migrator().HasTable("user_users"); !exist {
		log.Warn().Msg("user_users table does not exist, skipping foreign key constraint creation")
		log.Info().Msg("Note: Foreign key constraint will be added later when user service is running")
		return nil
	}
	log.Debug().Msg("user_users table exists")

	// 创建唯一索引：用户KSUID和合集名称的组合唯一（排除软删除的记录）
	if !db.Migrator().HasIndex("albums", "idx_albums_user_name") {
		log.Info().Msg("Creating unique index idx_albums_user_name")
		err := db.Exec(`
		CREATE UNIQUE INDEX idx_albums_user_name 
		ON albums(user_ksuid, album_name) 
		WHERE deleted_at IS NULL;
		`).Error
		if err != nil {
			log.Warn().
				Err(err).
				Msg("Failed to create unique index, but service will continue")
		} else {
			log.Info().Msg("Unique index created successfully")
		}
	} else {
		log.Debug().Msg("Unique index already exists")
	}

	// 尝试添加外键约束，如果失败则记录警告但不终止服务
	if exist := db.Migrator().HasConstraint("albums", "fk_albums_user_ksuid"); !exist {
		log.Info().Msg("Adding foreign key constraint fk_albums_user_ksuid")
		err := db.Exec(`
		ALTER TABLE albums
		ADD CONSTRAINT fk_albums_user_ksuid
		FOREIGN KEY (user_ksuid) REFERENCES user_users(user_ksuid) ON UPDATE CASCADE ON DELETE CASCADE;
		`).Error
		if err != nil {
			log.Warn().
				Err(err).
				Msg("Failed to add foreign key constraint, but service will continue")
			log.Info().Msg("Note: This may be due to existing data incompatibility or missing reference table")
			// 不返回错误，允许服务继续运行
		} else {
			log.Info().Msg("Foreign key constraint added successfully")
		}
	} else {
		log.Debug().Msg("Foreign key constraint already exists")
	}

	// 初始化合集内容表约束和索引
	if err := initAlbumContentConstraints(db); err != nil {
		log.Error().Err(err).Msg("Failed to initialize album content constraints")
		return err
	}

	log.Info().Msg("Album service database constraints initialized successfully")
	return nil
}

// initAlbumContentConstraints 初始化合集内容表约束和索引
func initAlbumContentConstraints(db *gorm.DB) error {
	log.Info().Msg("Initializing album content constraints and indexes")

	// 创建索引
	indexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_album_contents_album_ksuid ON album_contents(album_ksuid)",
		"CREATE INDEX IF NOT EXISTS idx_album_contents_content_ksuid ON album_contents(content_ksuid)",
		"CREATE INDEX IF NOT EXISTS idx_album_contents_content_type ON album_contents(content_type)",
		"CREATE INDEX IF NOT EXISTS idx_album_contents_sort_order ON album_contents(sort_order)",
		"CREATE INDEX IF NOT EXISTS idx_album_contents_created_at ON album_contents(created_at)",
		"CREATE INDEX IF NOT EXISTS idx_album_contents_deleted_at ON album_contents(deleted_at)",
		"CREATE UNIQUE INDEX IF NOT EXISTS idx_album_contents_unique ON album_contents(album_ksuid, content_ksuid) WHERE deleted_at IS NULL",
	}

	for _, indexSQL := range indexes {
		err := db.Exec(indexSQL).Error
		if err != nil {
			log.Error().Err(err).Str("sql", indexSQL).Msg("Failed to create index")
			return err
		}
	}

	log.Info().Msg("Album content indexes created successfully")

	// 添加外键约束（如果需要）
	// 注意：这里不添加到内容表的外键，因为内容在不同的服务中
	// 只添加到合集表的外键
	var count int64
	err := db.Raw(`
		SELECT COUNT(*)
		FROM information_schema.table_constraints
		WHERE constraint_name = 'fk_album_contents_album_ksuid'
		AND table_name = 'album_contents'
	`).Scan(&count).Error

	if err != nil {
		log.Error().Err(err).Msg("Failed to check album content foreign key constraint")
		return err
	}

	if count == 0 {
		log.Info().Msg("Adding album content foreign key constraint")
		err = db.Exec(`
		ALTER TABLE album_contents
		ADD CONSTRAINT fk_album_contents_album_ksuid
		FOREIGN KEY (album_ksuid) REFERENCES albums(album_ksuid) ON UPDATE CASCADE ON DELETE CASCADE;
		`).Error
		if err != nil {
			log.Warn().
				Err(err).
				Msg("Failed to add album content foreign key constraint, but service will continue")
			log.Info().Msg("Note: This may be due to existing data incompatibility")
			// 不返回错误，允许服务继续运行
		} else {
			log.Info().Msg("Album content foreign key constraint added successfully")
		}
	} else {
		log.Debug().Msg("Album content foreign key constraint already exists")
	}

	log.Info().Msg("Album content constraints initialized successfully")
	return nil
}

// createFavoriteItemTables 创建收藏项分表
func createFavoriteItemTables(db *gorm.DB) error {
	log.Info().Msg("Creating favorite item tables")

	// 获取所有分表名
	tableNames := model2.GetAllFavoriteItemTableNames()

	for _, tableName := range tableNames {
		// 为每个分表创建一个临时的FavoriteItem实例
		tempItem := &model2.FavoriteItem{}

		// 手动设置表名并创建表
		err := db.Table(tableName).AutoMigrate(tempItem)
		if err != nil {
			log.Error().
				Err(err).
				Str("table_name", tableName).
				Msg("Failed to create favorite item table")
			return fmt.Errorf("failed to create table %s: %w", tableName, err)
		}

		log.Debug().
			Str("table_name", tableName).
			Msg("Favorite item table created successfully")
	}

	log.Info().Msg("All favorite item tables created successfully")
	return nil
}

// createLikeTables 创建点赞分表
func createLikeTables(db *gorm.DB) error {
	log.Info().Msg("Creating like tables")

	// 获取所有分表名
	tableNames := model2.GetAllLikeItemTableNames()

	for _, tableName := range tableNames {
		// 为每个分表创建一个临时的LikeItem实例
		tempLike := &model2.LikeItem{}

		// 手动设置表名并创建表
		err := db.Table(tableName).AutoMigrate(tempLike)
		if err != nil {
			log.Error().
				Err(err).
				Str("table_name", tableName).
				Msg("Failed to create like table")
			return fmt.Errorf("failed to create table %s: %w", tableName, err)
		}

		// 创建索引
		indexes := []string{
			fmt.Sprintf("CREATE INDEX IF NOT EXISTS idx_%s_user_ksuid ON %s(user_ksuid)", tableName, tableName),
			fmt.Sprintf("CREATE INDEX IF NOT EXISTS idx_%s_content_ksuid ON %s(content_ksuid)", tableName, tableName),
			fmt.Sprintf("CREATE INDEX IF NOT EXISTS idx_%s_content_type ON %s(content_type)", tableName, tableName),
			fmt.Sprintf("CREATE INDEX IF NOT EXISTS idx_%s_type ON %s(type)", tableName, tableName),
			fmt.Sprintf("CREATE INDEX IF NOT EXISTS idx_%s_created_at ON %s(created_at)", tableName, tableName),
			fmt.Sprintf("CREATE UNIQUE INDEX IF NOT EXISTS idx_%s_user_content ON %s(user_ksuid, content_ksuid) WHERE deleted_at IS NULL", tableName, tableName),
		}

		for _, indexSQL := range indexes {
			err := db.Exec(indexSQL).Error
			if err != nil {
				log.Error().
					Err(err).
					Str("table_name", tableName).
					Str("sql", indexSQL).
					Msg("Failed to create index for like table")
				return fmt.Errorf("failed to create index for table %s: %w", tableName, err)
			}
		}

		log.Debug().
			Str("table_name", tableName).
			Msg("Like table created successfully")
	}

	log.Info().Msg("All like tables created successfully")
	return nil
}

// createLikeStatsTable 创建点赞统计表
func createLikeStatsTable(db *gorm.DB) error {
	log.Info().Msg("Creating like stats table")

	// 创建统计表
	err := db.AutoMigrate(&model2.LikeStats{})
	if err != nil {
		log.Error().
			Err(err).
			Msg("Failed to create like stats table")
		return fmt.Errorf("failed to create like stats table: %w", err)
	}

	// 创建索引
	indexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_like_stats_content_type ON interaction_like_stats(content_type)",
		"CREATE INDEX IF NOT EXISTS idx_like_stats_like_count ON interaction_like_stats(like_count)",
		"CREATE INDEX IF NOT EXISTS idx_like_stats_dislike_count ON interaction_like_stats(dislike_count)",
		"CREATE INDEX IF NOT EXISTS idx_like_stats_total_count ON interaction_like_stats(total_count)",
		"CREATE INDEX IF NOT EXISTS idx_like_stats_created_at ON interaction_like_stats(created_at)",
	}

	for _, indexSQL := range indexes {
		err := db.Exec(indexSQL).Error
		if err != nil {
			log.Error().
				Err(err).
				Str("sql", indexSQL).
				Msg("Failed to create index for like stats table")
			return fmt.Errorf("failed to create index for like stats table: %w", err)
		}
	}

	log.Info().Msg("Like stats table created successfully")
	return nil
}

// createPlayHistoryItemTables 创建播放历史分表
func createPlayHistoryItemTables(db *gorm.DB) error {
	log.Info().Msg("Creating play history item tables")

	// 获取所有分表名
	tableNames := model2.GetAllPlayHistoryItemTableNames()

	for _, tableName := range tableNames {
		// 为每个分表创建一个临时的PlayHistoryItem实例
		tempPlayHistory := &model2.PlayHistoryItem{}

		// 手动设置表名并创建表
		err := db.Table(tableName).AutoMigrate(tempPlayHistory)
		if err != nil {
			log.Error().
				Err(err).
				Str("table_name", tableName).
				Msg("Failed to create play history item table")
			return fmt.Errorf("failed to create table %s: %w", tableName, err)
		}

		// 创建索引
		indexes := []string{
			fmt.Sprintf("CREATE INDEX IF NOT EXISTS idx_%s_user_ksuid ON %s(user_ksuid)", tableName, tableName),
			fmt.Sprintf("CREATE INDEX IF NOT EXISTS idx_%s_content_ksuid ON %s(content_ksuid)", tableName, tableName),
			fmt.Sprintf("CREATE INDEX IF NOT EXISTS idx_%s_content_type ON %s(content_type)", tableName, tableName),
			fmt.Sprintf("CREATE INDEX IF NOT EXISTS idx_%s_play_duration ON %s(play_duration)", tableName, tableName),
			fmt.Sprintf("CREATE INDEX IF NOT EXISTS idx_%s_created_at ON %s(created_at)", tableName, tableName),
			fmt.Sprintf("CREATE INDEX IF NOT EXISTS idx_%s_updated_at ON %s(updated_at)", tableName, tableName),
			fmt.Sprintf("CREATE UNIQUE INDEX IF NOT EXISTS idx_%s_user_content ON %s(user_ksuid, content_ksuid) WHERE deleted_at IS NULL", tableName, tableName),
		}

		for _, indexSQL := range indexes {
			err := db.Exec(indexSQL).Error
			if err != nil {
				log.Error().
					Err(err).
					Str("table_name", tableName).
					Str("sql", indexSQL).
					Msg("Failed to create index for play history item table")
				return fmt.Errorf("failed to create index for table %s: %w", tableName, err)
			}
		}

		log.Debug().
			Str("table_name", tableName).
			Msg("Play history item table created successfully")
	}

	log.Info().Msg("All play history item tables created successfully")
	return nil
}

// createFavoriteStatsTable 创建收藏统计表
func createFavoriteStatsTable(db *gorm.DB) error {
	log.Info().Msg("Creating favorite stats table")

	// 创建统计表
	err := db.AutoMigrate(&model2.FavoriteStats{})
	if err != nil {
		log.Error().
			Err(err).
			Msg("Failed to create favorite stats table")
		return fmt.Errorf("failed to create favorite stats table: %w", err)
	}

	// 创建索引
	indexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_favorite_stats_content_type ON interaction_favorite_stats(content_type)",
		"CREATE INDEX IF NOT EXISTS idx_favorite_stats_favorite_count ON interaction_favorite_stats(favorite_count)",
		"CREATE INDEX IF NOT EXISTS idx_favorite_stats_created_at ON interaction_favorite_stats(created_at)",
	}

	for _, indexSQL := range indexes {
		err := db.Exec(indexSQL).Error
		if err != nil {
			log.Error().
				Err(err).
				Str("sql", indexSQL).
				Msg("Failed to create index for favorite stats table")
			return fmt.Errorf("failed to create index for favorite stats table: %w", err)
		}
	}

	log.Info().Msg("Favorite stats table created successfully")
	return nil
}
