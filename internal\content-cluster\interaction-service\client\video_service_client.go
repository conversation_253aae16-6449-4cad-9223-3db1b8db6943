package client

import (
	"fmt"
	"time"

	"pxpat-backend/pkg/httpclient"
)

// VideoServiceClient 视频服务客户端接口
type VideoServiceClient interface {

	// GetContentInfo 获取内容信息
	GetContentInfo(contentKSUID string) (*ContentInfo, error)

	// Favorite相关方法
	// BatchGetContentsByKSUIDs 批量获取内容信息
	BatchGetContentsByKSUIDs(contentKSUIDs []string) (map[string]*ContentInfo, error)
	// UpdateContentFavoriteCount 更新内容收藏数
	UpdateContentFavoriteCount(contentKSUID string, increment bool) error
}

// videoServiceClient 视频服务客户端实现
type videoServiceClient struct {
	httpClient *httpclient.HTTPClient
}

// VideoServiceConfig 视频服务客户端配置
type VideoServiceConfig struct {
	BaseURL string
	Timeout time.Duration
}

// BatchAddContentsToAlbumRequest 批量添加内容到合集请求
type BatchAddContentsToAlbumRequest struct {
	AlbumKSUID string             `json:"album_ksuid"`
	Contents   []BatchContentItem `json:"contents"`
}

// BatchRemoveContentsFromAlbumRequest 批量从合集移除内容请求
type BatchRemoveContentsFromAlbumRequest struct {
	AlbumKSUID    string   `json:"album_ksuid"`
	ContentKSUIDs []string `json:"content_ksuids"`
}

// BatchContentItem 批量内容项
type BatchContentItem struct {
	ContentKSUID string `json:"content_ksuid"`
	ContentType  string `json:"content_type"`
	SortOrder    int    `json:"sort_order"`
}

// BatchOperationResult 批量操作结果
type BatchOperationResult struct {
	SuccessCount int               `json:"success_count"`
	FailedCount  int               `json:"failed_count"`
	FailedItems  []BatchFailedItem `json:"failed_items"`
}

// BatchFailedItem 批量操作失败项
type BatchFailedItem struct {
	ContentKSUID string `json:"content_ksuid"`
	Error        string `json:"error"`
}

// AlbumContentInfo 合集内容信息
type AlbumContentInfo struct {
	ContentKSUID string    `json:"content_ksuid"`
	ContentType  string    `json:"content_type"`
	SortOrder    int       `json:"sort_order"`
	AddedAt      time.Time `json:"added_at"`
	// 内容详细信息
	Title       string  `json:"title,omitempty"`
	Description string  `json:"description,omitempty"`
	CoverURL    string  `json:"cover_url,omitempty"`
	Duration    float64 `json:"duration,omitempty"`
	ViewCount   int64   `json:"view_count,omitempty"`
	LikeCount   int64   `json:"like_count,omitempty"`
}

// ContentInfo 内容信息
type ContentInfo struct {
	UserKSUID     string  `json:"user_ksuid"`
	ContentKSUID  string  `json:"content_ksuid"`
	ContentType   string  `json:"content_type"`
	Title         string  `json:"title"`
	CoverURL      string  `json:"cover_url"`
	Duration      float64 `json:"duration"`
	ViewCount     int64   `json:"view_count"`
	LikeCount     int64   `json:"like_count"`
	CommentCount  int64   `json:"comment_count"`
	FavoriteCount int64   `json:"favorite_count"`
}

// NewVideoServiceClient 创建视频服务客户端
func NewVideoServiceClient(config VideoServiceConfig) VideoServiceClient {
	return &videoServiceClient{
		httpClient: httpclient.NewHTTPClient(httpclient.ClientConfig{
			BaseURL:          config.BaseURL,
			Timeout:          config.Timeout,
			RetryCount:       3,
			RetryWaitTime:    1 * time.Second,
			RetryMaxWaitTime: 5 * time.Second,
		}),
	}
}

// GetContentInfo 获取内容信息
func (c *videoServiceClient) GetContentInfo(contentKSUID string) (*ContentInfo, error) {
	var response struct {
		Code int         `json:"code"`
		Data ContentInfo `json:"data"`
	}

	err := c.httpClient.Get(fmt.Sprintf("/api/v1/content/%s", contentKSUID), &response)
	if err != nil {
		return nil, fmt.Errorf("failed to get content info: %w", err)
	}

	if response.Code < 0 || response.Code > 9999 {
		return nil, fmt.Errorf("video service returned error code: %d", response.Code)
	}

	return &response.Data, nil
}

// BatchGetContentsByKSUIDs 批量获取内容信息
func (c *videoServiceClient) BatchGetContentsByKSUIDs(contentKSUIDs []string) (map[string]*ContentInfo, error) {
	var response struct {
		Code int                     `json:"code"`
		Data map[string]*ContentInfo `json:"data"`
	}

	requestBody := struct {
		ContentKSUIDs []string `json:"content_ksuids"`
	}{
		ContentKSUIDs: contentKSUIDs,
	}

	err := c.httpClient.Post("/api/v1/intra/content/batch", requestBody, &response)
	if err != nil {
		return nil, fmt.Errorf("failed to batch get contents: %w", err)
	}

	if response.Code != 0 {
		return nil, fmt.Errorf("video service returned error code: %d", response.Code)
	}

	return response.Data, nil
}

// UpdateContentFavoriteCount 更新内容收藏数
func (c *videoServiceClient) UpdateContentFavoriteCount(contentKSUID string, increment bool) error {
	var response struct {
		Code int `json:"code"`
	}

	requestBody := struct {
		Increment bool `json:"increment"`
	}{
		Increment: increment,
	}

	err := c.httpClient.Put(fmt.Sprintf("/api/v1/content/%s/favorite-count", contentKSUID), requestBody, &response)
	if err != nil {
		return fmt.Errorf("failed to update content favorite count: %w", err)
	}

	if response.Code != 200 {
		return fmt.Errorf("video service returned error code: %d", response.Code)
	}

	return nil
}
