package impl

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"pxpat-backend/internal/user-cluster/user-service/model"
	"pxpat-backend/pkg/ksuid"
)

// FollowRepositoryTestSuite 关注仓库测试套件
type FollowRepositoryTestSuite struct {
	suite.Suite
	db   *gorm.DB
	repo *FollowRepositoryImpl
	ctx  context.Context
}

func (suite *FollowRepositoryTestSuite) SetupSuite() {
	// 创建内存数据库
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)

	// 自动迁移表结构
	err = db.AutoMigrate(
		&model.User{},
		&model.UserFollow{},
	)
	suite.Require().NoError(err)

	suite.db = db
	suite.repo = NewFollowRepository(db).(*FollowRepositoryImpl)
	suite.ctx = context.Background()
}

func (suite *FollowRepositoryTestSuite) SetupTest() {
	// 创建测试用户
	users := []*model.User{
		{
			UserKSUID:      "test_user_001",
			Email:          "<EMAIL>",
			Username:       "user1",
			Nickname:       "用户1",
			FollowersCount: 0,
			FollowingCount: 0,
		},
		{
			UserKSUID:      "test_user_002",
			Email:          "<EMAIL>",
			Username:       "user2",
			Nickname:       "用户2",
			FollowersCount: 0,
			FollowingCount: 0,
		},
		{
			UserKSUID:      "test_user_003",
			Email:          "<EMAIL>",
			Username:       "user3",
			Nickname:       "用户3",
			FollowersCount: 0,
			FollowingCount: 0,
		},
	}

	for _, user := range users {
		suite.db.Create(user)
	}
}

func (suite *FollowRepositoryTestSuite) TearDownTest() {
	// 清理测试数据
	suite.db.Exec("DELETE FROM user_follows")
	suite.db.Exec("DELETE FROM users")
}

func (suite *FollowRepositoryTestSuite) TestCreateFollow() {
	followerKSUID := "test_user_001"
	followeeKSUID := "test_user_002"

	// 测试创建关注关系
	follow := model.NewUserFollow(followerKSUID, followeeKSUID)
	err := suite.repo.CreateFollow(suite.ctx, follow)
	assert.NoError(suite.T(), err)

	// 验证关注关系是否创建成功
	isFollowing, err := suite.repo.IsFollowing(suite.ctx, followerKSUID, followeeKSUID)
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), isFollowing)

	// 测试重复关注（应该失败）
	follow2 := model.NewUserFollow(followerKSUID, followeeKSUID)
	err = suite.repo.CreateFollow(suite.ctx, follow2)
	assert.Error(suite.T(), err)
}

func (suite *FollowRepositoryTestSuite) TestDeleteFollow() {
	followerKSUID := "test_user_001"
	followeeKSUID := "test_user_002"

	// 先创建关注关系
	follow := model.NewUserFollow(followerKSUID, followeeKSUID)
	err := suite.repo.CreateFollow(suite.ctx, follow)
	assert.NoError(suite.T(), err)

	// 测试删除关注关系
	err = suite.repo.DeleteFollow(suite.ctx, followerKSUID, followeeKSUID)
	assert.NoError(suite.T(), err)

	// 验证关注关系是否删除成功
	isFollowing, err := suite.repo.IsFollowing(suite.ctx, followerKSUID, followeeKSUID)
	assert.NoError(suite.T(), err)
	assert.False(suite.T(), isFollowing)

	// 测试删除不存在的关注关系（应该不报错）
	err = suite.repo.DeleteFollow(suite.ctx, followerKSUID, followeeKSUID)
	assert.NoError(suite.T(), err)
}

func (suite *FollowRepositoryTestSuite) TestIsFollowing() {
	followerKSUID := "test_user_001"
	followeeKSUID := "test_user_002"

	// 测试未关注状态
	isFollowing, err := suite.repo.IsFollowing(suite.ctx, followerKSUID, followeeKSUID)
	assert.NoError(suite.T(), err)
	assert.False(suite.T(), isFollowing)

	// 创建关注关系
	follow := model.NewUserFollow(followerKSUID, followeeKSUID)
	err = suite.repo.CreateFollow(suite.ctx, follow)
	assert.NoError(suite.T(), err)

	// 测试已关注状态
	isFollowing, err = suite.repo.IsFollowing(suite.ctx, followerKSUID, followeeKSUID)
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), isFollowing)
}

func (suite *FollowRepositoryTestSuite) TestGetFollowers() {
	followeeKSUID := "test_user_001"
	follower1KSUID := "test_user_002"
	follower2KSUID := "test_user_003"

	// 创建关注关系
	follow1 := model.NewUserFollow(follower1KSUID, followeeKSUID)
	err := suite.repo.CreateFollow(suite.ctx, follow1)
	assert.NoError(suite.T(), err)
	follow2 := model.NewUserFollow(follower2KSUID, followeeKSUID)
	err = suite.repo.CreateFollow(suite.ctx, follow2)
	assert.NoError(suite.T(), err)

	// 测试获取粉丝列表
	followers, total, err := suite.repo.GetFollowers(suite.ctx, followeeKSUID, 1, 10)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), int64(2), total)
	assert.Len(suite.T(), followers, 2)

	// 验证粉丝信息
	followerKSUIDs := make([]string, len(followers))
	for i, follower := range followers {
		followerKSUIDs[i] = follower.UserKSUID
	}
	assert.Contains(suite.T(), followerKSUIDs, follower1KSUID)
	assert.Contains(suite.T(), followerKSUIDs, follower2KSUID)
}

func (suite *FollowRepositoryTestSuite) TestGetFollowing() {
	followerKSUID := "test_user_001"
	followee1KSUID := "test_user_002"
	followee2KSUID := "test_user_003"

	// 创建关注关系
	follow1 := model.NewUserFollow(followerKSUID, followee1KSUID)
	err := suite.repo.CreateFollow(suite.ctx, follow1)
	assert.NoError(suite.T(), err)
	follow2 := model.NewUserFollow(followerKSUID, followee2KSUID)
	err = suite.repo.CreateFollow(suite.ctx, follow2)
	assert.NoError(suite.T(), err)

	// 测试获取关注列表
	following, total, err := suite.repo.GetFollowing(suite.ctx, followerKSUID, 1, 10)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), int64(2), total)
	assert.Len(suite.T(), following, 2)

	// 验证关注信息
	followeeKSUIDs := make([]string, len(following))
	for i, followee := range following {
		followeeKSUIDs[i] = followee.UserKSUID
	}
	assert.Contains(suite.T(), followeeKSUIDs, followee1KSUID)
	assert.Contains(suite.T(), followeeKSUIDs, followee2KSUID)
}

func (suite *FollowRepositoryTestSuite) TestGetFollowersCount() {
	followeeKSUID := "test_user_001"
	follower1KSUID := "test_user_002"
	follower2KSUID := "test_user_003"

	// 测试初始粉丝数量
	count, err := suite.repo.GetFollowersCount(suite.ctx, followeeKSUID)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), int64(0), count)

	// 创建关注关系
	follow1 := model.NewUserFollow(follower1KSUID, followeeKSUID)
	err = suite.repo.CreateFollow(suite.ctx, follow1)
	assert.NoError(suite.T(), err)
	follow2 := model.NewUserFollow(follower2KSUID, followeeKSUID)
	err = suite.repo.CreateFollow(suite.ctx, follow2)
	assert.NoError(suite.T(), err)

	// 测试粉丝数量
	count, err = suite.repo.GetFollowersCount(suite.ctx, followeeKSUID)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), int64(2), count)
}

func (suite *FollowRepositoryTestSuite) TestGetFollowingCount() {
	followerKSUID := "test_user_001"
	followee1KSUID := "test_user_002"
	followee2KSUID := "test_user_003"

	// 测试初始关注数量
	count, err := suite.repo.GetFollowingCount(suite.ctx, followerKSUID)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), int64(0), count)

	// 创建关注关系
	follow1 := model.NewUserFollow(followerKSUID, followee1KSUID)
	err = suite.repo.CreateFollow(suite.ctx, follow1)
	assert.NoError(suite.T(), err)
	follow2 := model.NewUserFollow(followerKSUID, followee2KSUID)
	err = suite.repo.CreateFollow(suite.ctx, follow2)
	assert.NoError(suite.T(), err)

	// 测试关注数量
	count, err = suite.repo.GetFollowingCount(suite.ctx, followerKSUID)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), int64(2), count)
}

func (suite *FollowRepositoryTestSuite) TestUpdateUserFollowCounts() {
	followerKSUID := "test_user_001"
	followeeKSUID := "test_user_002"

	// 创建关注关系
	follow := model.NewUserFollow(followerKSUID, followeeKSUID)
	err := suite.repo.CreateFollow(suite.ctx, follow)
	assert.NoError(suite.T(), err)

	// 更新关注者的关注数量
	err = suite.repo.UpdateUserFollowCounts(suite.ctx, followerKSUID)
	assert.NoError(suite.T(), err)

	// 更新被关注者的粉丝数量
	err = suite.repo.UpdateUserFollowCounts(suite.ctx, followeeKSUID)
	assert.NoError(suite.T(), err)

	// 验证用户表中的数量是否正确更新
	var follower model.User
	err = suite.db.Where("user_ksuid = ?", followerKSUID).First(&follower).Error
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), int64(1), follower.FollowingCount)

	var followee model.User
	err = suite.db.Where("user_ksuid = ?", followeeKSUID).First(&followee).Error
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), int64(1), followee.FollowersCount)
}

func (suite *FollowRepositoryTestSuite) TestBatchCheckFollowStatus() {
	followerKSUID := "test_user_001"
	followee1KSUID := "test_user_002"
	followee2KSUID := "test_user_003"

	// 创建一个关注关系
	follow := model.NewUserFollow(followerKSUID, followee1KSUID)
	err := suite.repo.CreateFollow(suite.ctx, follow)
	assert.NoError(suite.T(), err)

	// 批量检查关注状态
	followeeKSUIDs := []string{followee1KSUID, followee2KSUID}
	statuses, err := suite.repo.BatchCheckFollowStatus(suite.ctx, followerKSUID, followeeKSUIDs)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), statuses, 2)

	// 验证关注状态
	statusMap := make(map[string]bool)
	for _, status := range statuses {
		statusMap[status.UserKSUID] = status.IsFollowing
	}
	assert.True(suite.T(), statusMap[followee1KSUID])
	assert.False(suite.T(), statusMap[followee2KSUID])
}

func (suite *FollowRepositoryTestSuite) TestGetMutualFollows() {
	user1KSUID := "test_user_001"
	user2KSUID := "test_user_002"
	user3KSUID := "test_user_003"

	// 创建互相关注关系
	follow1 := model.NewUserFollow(user1KSUID, user2KSUID)
	err := suite.repo.CreateFollow(suite.ctx, follow1)
	assert.NoError(suite.T(), err)
	follow2 := model.NewUserFollow(user2KSUID, user1KSUID)
	err = suite.repo.CreateFollow(suite.ctx, follow2)
	assert.NoError(suite.T(), err)

	// 创建单向关注关系
	follow3 := model.NewUserFollow(user1KSUID, user3KSUID)
	err = suite.repo.CreateFollow(suite.ctx, follow3)
	assert.NoError(suite.T(), err)

	// 测试获取互相关注列表
	mutualFollows, total, err := suite.repo.GetMutualFollows(suite.ctx, user1KSUID, 1, 10)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), int64(1), total)
	assert.Len(suite.T(), mutualFollows, 1)
	assert.Equal(suite.T(), user2KSUID, mutualFollows[0].UserKSUID)
}

func (suite *FollowRepositoryTestSuite) TestPagination() {
	followeeKSUID := "test_user_001"

	// 创建多个关注关系（超过一页）
	for i := 0; i < 15; i++ {
		followerKSUID := ksuid.GenerateKSUID()
		// 创建测试用户
		user := &model.User{
			UserKSUID: followerKSUID,
			Email:     followerKSUID + "@test.com",
			Username:  "user_" + followerKSUID[:8],
			Nickname:  "用户_" + followerKSUID[:8],
		}
		suite.db.Create(user)

		follow := model.NewUserFollow(followerKSUID, followeeKSUID)
		err := suite.repo.CreateFollow(suite.ctx, follow)
		assert.NoError(suite.T(), err)
	}

	// 测试第一页
	followers, total, err := suite.repo.GetFollowers(suite.ctx, followeeKSUID, 1, 10)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), int64(15), total)
	assert.Len(suite.T(), followers, 10)

	// 测试第二页
	followers, total, err = suite.repo.GetFollowers(suite.ctx, followeeKSUID, 2, 10)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), int64(15), total)
	assert.Len(suite.T(), followers, 5)
}

func (suite *FollowRepositoryTestSuite) TestSoftDelete() {
	followerKSUID := "test_user_001"
	followeeKSUID := "test_user_002"

	// 创建关注关系
	follow := model.NewUserFollow(followerKSUID, followeeKSUID)
	err := suite.repo.CreateFollow(suite.ctx, follow)
	assert.NoError(suite.T(), err)

	// 删除关注关系（软删除）
	err = suite.repo.DeleteFollow(suite.ctx, followerKSUID, followeeKSUID)
	assert.NoError(suite.T(), err)

	// 验证关注关系不存在
	isFollowing, err := suite.repo.IsFollowing(suite.ctx, followerKSUID, followeeKSUID)
	assert.NoError(suite.T(), err)
	assert.False(suite.T(), isFollowing)

	// 重新创建关注关系（应该成功）
	follow2 := model.NewUserFollow(followerKSUID, followeeKSUID)
	err = suite.repo.CreateFollow(suite.ctx, follow2)
	assert.NoError(suite.T(), err)

	// 验证关注关系重新存在
	isFollowing, err = suite.repo.IsFollowing(suite.ctx, followerKSUID, followeeKSUID)
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), isFollowing)
}

func TestFollowRepositoryTestSuite(t *testing.T) {
	suite.Run(t, new(FollowRepositoryTestSuite))
}
